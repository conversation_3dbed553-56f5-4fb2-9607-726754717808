#!/usr/bin/env python3
"""
AutoSpace System Test Script
Tests the complete functionality including authentication, session management, and tool execution.
"""

import requests
import json
import time
import os
import sys

class AutoSpaceTest:
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
        
    def log_test(self, test_name, success, message=""):
        """Log test result"""
        status = "PASS" if success else "FAIL"
        self.test_results.append({
            'test': test_name,
            'status': status,
            'message': message
        })
        print(f"[{status}] {test_name}: {message}")
    
    def test_login(self):
        """Test user authentication"""
        try:
            # Test login page access
            response = self.session.get(f"{self.base_url}/login")
            if response.status_code == 200:
                self.log_test("Login Page Access", True, "Login page accessible")
            else:
                self.log_test("Login Page Access", False, f"Status: {response.status_code}")
                return False
            
            # Test login with default credentials
            login_data = {
                "username": "admin",
                "password": "admin"
            }
            response = self.session.post(
                f"{self.base_url}/login",
                json=login_data,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('message') == 'Login successful':
                    self.log_test("User Authentication", True, "Login successful")
                    return True
                else:
                    self.log_test("User Authentication", False, f"Unexpected response: {data}")
            else:
                self.log_test("User Authentication", False, f"Status: {response.status_code}")
            
        except Exception as e:
            self.log_test("User Authentication", False, f"Exception: {str(e)}")
        
        return False
    
    def test_main_page_access(self):
        """Test main application page access"""
        try:
            response = self.session.get(f"{self.base_url}/")
            if response.status_code == 200:
                self.log_test("Main Page Access", True, "Main page accessible after login")
                return True
            else:
                self.log_test("Main Page Access", False, f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("Main Page Access", False, f"Exception: {str(e)}")
        
        return False
    
    def test_api_endpoints(self):
        """Test API endpoints"""
        try:
            # Test user info endpoint
            response = self.session.get(f"{self.base_url}/api/user")
            if response.status_code == 200:
                data = response.json()
                if data.get('username') == 'admin':
                    self.log_test("User Info API", True, "User info retrieved successfully")
                else:
                    self.log_test("User Info API", False, f"Unexpected data: {data}")
            else:
                self.log_test("User Info API", False, f"Status: {response.status_code}")
            
            # Test sessions endpoint
            response = self.session.get(f"{self.base_url}/api/sessions")
            if response.status_code == 200:
                self.log_test("Sessions API", True, "Sessions endpoint accessible")
            else:
                self.log_test("Sessions API", False, f"Status: {response.status_code}")
            
            # Test tools endpoint
            response = self.session.get(f"{self.base_url}/api/tools")
            if response.status_code == 200:
                data = response.json()
                if len(data) > 0:
                    self.log_test("Tools API", True, f"Found {len(data)} tools")
                else:
                    self.log_test("Tools API", False, "No tools found")
            else:
                self.log_test("Tools API", False, f"Status: {response.status_code}")
                
        except Exception as e:
            self.log_test("API Endpoints", False, f"Exception: {str(e)}")
    
    def test_file_upload_and_processing(self):
        """Test file upload and tool processing"""
        try:
            # Create a test file
            test_content = "Line 1: Test content\nLine 2: More test content\nLine 3: Final line"
            
            # Test upload
            files = {'file': ('test.txt', test_content, 'text/plain')}
            data = {
                'tool': 'extractor',
                'mode': 'fast'
            }
            
            response = self.session.post(
                f"{self.base_url}/upload",
                files=files,
                data=data
            )
            
            if response.status_code == 200:
                upload_data = response.json()
                if upload_data.get('message') == 'success':
                    session_id = upload_data.get('session_id')
                    self.log_test("File Upload", True, f"File uploaded, session: {session_id}")
                    
                    # Test tool execution
                    run_data = {'session_id': session_id}
                    response = self.session.post(
                        f"{self.base_url}/runtool",
                        json=run_data,
                        headers={'Content-Type': 'application/json'}
                    )
                    
                    if response.status_code == 200:
                        self.log_test("Tool Execution", True, "Tool execution started")
                        
                        # Wait a bit and check session status
                        time.sleep(2)
                        response = self.session.get(f"{self.base_url}/api/sessions/{session_id}/progress")
                        if response.status_code == 200:
                            progress_data = response.json()
                            self.log_test("Session Progress", True, f"Status: {progress_data.get('status')}")
                        else:
                            self.log_test("Session Progress", False, f"Status: {response.status_code}")
                    else:
                        self.log_test("Tool Execution", False, f"Status: {response.status_code}")
                else:
                    self.log_test("File Upload", False, f"Upload failed: {upload_data}")
            else:
                self.log_test("File Upload", False, f"Status: {response.status_code}")
                
        except Exception as e:
            self.log_test("File Upload and Processing", False, f"Exception: {str(e)}")
    
    def test_python_tools(self):
        """Test that Python tools exist and are executable"""
        tools = [
            'extractor.py',
            'acrobat_sim.py', 
            'equal_manual.py',
            'keyword_search.py',
            'meta_data.py',
            'part_search.py',
            'magic.py',
            'image_compare.py',
            'catalogue_compare.py'
        ]
        
        tools_dir = 'python_tools'
        missing_tools = []
        
        for tool in tools:
            tool_path = os.path.join(tools_dir, tool)
            if not os.path.exists(tool_path):
                missing_tools.append(tool)
        
        if not missing_tools:
            self.log_test("Python Tools", True, f"All {len(tools)} tools found")
        else:
            self.log_test("Python Tools", False, f"Missing tools: {missing_tools}")
    
    def test_logout(self):
        """Test user logout"""
        try:
            response = self.session.get(f"{self.base_url}/logout")
            if response.status_code == 200:
                data = response.json()
                if data.get('message') == 'Logged out successfully':
                    self.log_test("User Logout", True, "Logout successful")
                    
                    # Verify we can't access protected pages
                    response = self.session.get(f"{self.base_url}/")
                    if response.status_code == 401:
                        self.log_test("Logout Verification", True, "Protected page inaccessible after logout")
                    else:
                        self.log_test("Logout Verification", False, "Protected page still accessible")
                else:
                    self.log_test("User Logout", False, f"Unexpected response: {data}")
            else:
                self.log_test("User Logout", False, f"Status: {response.status_code}")
                
        except Exception as e:
            self.log_test("User Logout", False, f"Exception: {str(e)}")
    
    def run_all_tests(self):
        """Run all tests"""
        print("=" * 60)
        print("AUTOSPACE SYSTEM TESTS")
        print("=" * 60)
        
        # Test sequence
        if self.test_login():
            self.test_main_page_access()
            self.test_api_endpoints()
            self.test_file_upload_and_processing()
            self.test_logout()
        
        # Test tools independently
        self.test_python_tools()
        
        # Print summary
        print("\n" + "=" * 60)
        print("TEST SUMMARY")
        print("=" * 60)
        
        passed = len([r for r in self.test_results if r['status'] == 'PASS'])
        failed = len([r for r in self.test_results if r['status'] == 'FAIL'])
        
        print(f"Total Tests: {len(self.test_results)}")
        print(f"Passed: {passed}")
        print(f"Failed: {failed}")
        print(f"Success Rate: {(passed/len(self.test_results)*100):.1f}%")
        
        if failed > 0:
            print("\nFAILED TESTS:")
            for result in self.test_results:
                if result['status'] == 'FAIL':
                    print(f"  - {result['test']}: {result['message']}")
        
        return failed == 0

def main():
    """Main function"""
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = "http://localhost:5000"
    
    print(f"Testing AutoSpace at: {base_url}")
    print("Make sure the Flask server is running before starting tests.\n")
    
    tester = AutoSpaceTest(base_url)
    success = tester.run_all_tests()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
