import sys
import fitz
import json
import concurrent.futures

class Word:
    def __init__(self, word_text, word_start, word_end, word_up, word_down, word_page):
        self.word_text = word_text
        self.word_start = word_start
        self.word_end = word_end
        self.word_up = word_up
        self.word_down = word_down
        self.word_page = word_page

    def __eq__(self, other):
        return (self.word_text == other.word_text and self.word_start == other.word_start
                and self.word_end == other.word_end and self.word_up == other.word_up)

    def __hash__(self):
        return hash((self.word_text, self.word_start, self.word_end, self.word_up))

    @property
    def word_coordinate(self):
        return self.word_up + self.word_down


class Line:
    def __init__(self, line_words):
        self.line_words = line_words

    @property
    def full_line(self):
        return " ".join([word.word_text for word in self.line_words])

    @property
    def line_up(self):
        return min([word.word_up for word in self.line_words])

    @property
    def line_down(self):
        return max([word.word_down for word in self.line_words])

    @property
    def line_start(self):
        return min([word.word_start for word in self.line_words])

    @property
    def line_end(self):
        return max([word.word_end for word in self.line_words])

    @property
    def line_page(self):
        return self.line_words[0].word_page


def extract_all_words(link):
    link = link.removesuffix('\n')
    doc_1 = fitz.open(link.removesuffix('\n'))
    all_words = []
    for page_index, page in enumerate(doc_1):
        for ext_word in page.get_text("words"):
            all_words.append([ext_word, page_index])
    return all_words


def fitz_extract_all_words(link):
    start_index, end_index, up_index, down_index, text_index = 0, 2, 1, 3, 4
    list_of_lines = []

    link = link.removesuffix('\n')
    doc_1 = fitz.open(link)

    for page_index, page in enumerate(doc_1):
        word_properties_list = [
            Word(w[text_index], w[start_index], w[end_index], w[up_index], w[down_index], page_index)
            for w in page.get_text("words")]

        line_list_of_words = []
        found_words_list = []

        for fixed_word in word_properties_list:
            if fixed_word in found_words_list:
                continue
            for looping_word in word_properties_list:
                if (looping_word.word_up - 4 <= fixed_word.word_up <= looping_word.word_up + 4
                        or looping_word.word_down - 4 <= fixed_word.word_down <= looping_word.word_down + 4):
                    line_list_of_words.append(looping_word)
                    found_words_list.append(looping_word)

            line_list_of_words = list(set(line_list_of_words))
            line_list_of_words.sort(key=lambda x: x.word_start)
            list_of_lines.append(
                Line(line_list_of_words))

            line_list_of_words = []
    list_of_lines.sort(key=lambda x: x.line_up)
    list_of_lines.sort(key=lambda x: x.line_page)

    return list_of_lines


def count_of_pages(link):
    doc = fitz.open(link)
    return doc.page_count

def search_for_keyword(keyword, full_text):
    if keyword.lower().strip() in full_text.lower():
        return "Found"
    return "Not Found"
    
    

def main(url):
    try:
        link = url.strip()
        link = link.split("\t")[0]
        keywords = url.split("\t")[1].split("|")
        mode = url.split("\t")[-1].strip()
        found_keywords = []

        '''Get  number of pages in the document'''
        num_pages = count_of_pages(link.strip())

        if num_pages > 10000:
            return link.strip() + "\t" + "Page limit exceeded"

        text = [line.full_line.lower() for line in fitz_extract_all_words(link.strip())]
        full_text = "\n".join(text)
        
        if mode == 'keyword':
            for keyword in keywords:
                if search_for_keyword(keyword, full_text) == "Found":
                    found_keywords.append(keyword)
        elif mode == 'line':
            pass
        
        final_row = []
        final_row.insert(0, "|".join(found_keywords).strip())
        final_row.insert(0, str(num_pages))
        final_row.insert(0, str(len(full_text)))
        final_row.insert(0, "keyword found" if found_keywords else "no keywords found")
        final_row.insert(0, "searchable" if len(full_text) / num_pages > 200 else "unsearchable")
        final_row.insert(0, "DONE")
        final_row.insert(0, link.strip())
        return "\t".join(final_row)

    except Exception as E:
        return link.strip() + "\t" + "Error" + "\t" + str(E)
    

if __name__ == "__main__":

    input_path = sys.argv[1]
    output_path = sys.argv[2]
    mode = sys.argv[3]

    with open(input_path, "r") as input_file:
        links_list = input_file.read()
        links_list = json.loads(links_list)

    with open(output_path, "a", encoding='utf8') as output_file:
        pass 

    links_list = list(links_list.values())[:-1]

    if mode == "normal":
        for link in links_list:
            with open(output_path, "a", encoding='utf8') as output_file:
                output_file.write(main(link))
                output_file.write("\n")

    elif mode == "fast":
        one_time_count = 100
        with concurrent.futures.ProcessPoolExecutor(max_workers=7) as executor1:
            for i in range(1, len(links_list), one_time_count):
                batch_links = links_list[i:i + one_time_count]
                results = executor1.map(main, batch_links)
                for result in results:
                    with open(output_path, 'a', encoding='utf8') as of:
                        of.write(result)
                        of.write('\n')