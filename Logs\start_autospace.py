#!/usr/bin/env python3
"""
AutoSpace Startup Script
Helps with starting the AutoSpace server and provides debugging information.
"""

import os
import sys
import subprocess
import time
import requests
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed"""
    print("Checking dependencies...")
    
    try:
        import flask
        print(f"✓ Flask {flask.__version__} installed")
    except ImportError:
        print("✗ Flask not installed. Run: pip install flask")
        return False
    
    return True

def check_file_structure():
    """Check if all required files exist"""
    print("\nChecking file structure...")
    
    required_files = [
        'backend.py',
        'templates/index.html',
        'templates/login.html',
        'static/styles.css',
        'static/data.js'
    ]
    
    required_dirs = [
        'python_tools',
        'templates',
        'static'
    ]
    
    missing_files = []
    missing_dirs = []
    
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            print(f"✓ {file_path}")
    
    for dir_path in required_dirs:
        if not os.path.exists(dir_path):
            missing_dirs.append(dir_path)
        else:
            print(f"✓ {dir_path}/")
    
    if missing_files or missing_dirs:
        print("\n✗ Missing files/directories:")
        for item in missing_files + missing_dirs:
            print(f"  - {item}")
        return False
    
    return True

def check_python_tools():
    """Check if Python tools exist"""
    print("\nChecking Python tools...")
    
    tools = [
        'extractor.py',
        'acrobat_sim.py',
        'equal_manual.py',
        'keyword_search.py',
        'meta_data.py',
        'part_search.py',
        'magic.py',
        'image_compare.py',
        'catalogue_compare.py'
    ]
    
    tools_dir = Path('python_tools')
    missing_tools = []
    
    for tool in tools:
        tool_path = tools_dir / tool
        if tool_path.exists():
            print(f"✓ {tool}")
        else:
            missing_tools.append(tool)
            print(f"✗ {tool}")
    
    if missing_tools:
        print(f"\n⚠ Missing {len(missing_tools)} tools, but server can still run")
    
    return True

def create_directories():
    """Create required directories if they don't exist"""
    print("\nCreating required directories...")
    
    dirs = ['AutoSpace_output_files', 'python_tools', 'templates', 'static']
    
    for dir_name in dirs:
        if not os.path.exists(dir_name):
            os.makedirs(dir_name)
            print(f"✓ Created {dir_name}/")
        else:
            print(f"✓ {dir_name}/ exists")

def test_server_connection(port=5000, max_attempts=10):
    """Test if the server is responding"""
    print(f"\nTesting server connection on port {port}...")
    
    for attempt in range(max_attempts):
        try:
            response = requests.get(f'http://localhost:{port}/health', timeout=2)
            if response.status_code == 200:
                data = response.json()
                print(f"✓ Server is running - {data.get('service')} v{data.get('version')}")
                return True
        except requests.exceptions.RequestException:
            pass
        
        if attempt < max_attempts - 1:
            print(f"  Attempt {attempt + 1}/{max_attempts} - waiting...")
            time.sleep(2)
    
    print("✗ Server is not responding")
    return False

def start_server():
    """Start the AutoSpace server"""
    print("\n" + "="*60)
    print("STARTING AUTOSPACE SERVER")
    print("="*60)
    
    # Check prerequisites
    if not check_dependencies():
        print("\n✗ Dependencies check failed")
        return False
    
    if not check_file_structure():
        print("\n✗ File structure check failed")
        return False
    
    check_python_tools()
    create_directories()
    
    print("\n" + "="*60)
    print("LAUNCHING SERVER...")
    print("="*60)
    print("Server will start on: http://localhost:5000")
    print("Login credentials: admin / admin")
    print("Press Ctrl+C to stop the server")
    print("="*60)
    
    try:
        # Start the Flask server
        subprocess.run([sys.executable, 'backend.py'], check=True)
    except KeyboardInterrupt:
        print("\n\nServer stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"\n✗ Server failed to start: {e}")
        return False
    except FileNotFoundError:
        print("\n✗ backend.py not found")
        return False
    
    return True

def main():
    """Main function"""
    print("AutoSpace Startup Script")
    print("=" * 40)
    
    # Check if we're in the right directory
    if not os.path.exists('backend.py'):
        print("✗ backend.py not found in current directory")
        print("Please run this script from the AutoSpace project directory")
        sys.exit(1)
    
    # Parse command line arguments
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == 'check':
            print("Running system checks only...\n")
            deps_ok = check_dependencies()
            files_ok = check_file_structure()
            check_python_tools()
            
            if deps_ok and files_ok:
                print("\n✓ All checks passed! Ready to start server.")
                sys.exit(0)
            else:
                print("\n✗ Some checks failed. Please fix issues before starting.")
                sys.exit(1)
        
        elif command == 'test':
            print("Testing server connection...\n")
            if test_server_connection():
                sys.exit(0)
            else:
                print("\nMake sure the server is running with: python start_autospace.py")
                sys.exit(1)
        
        elif command == 'help':
            print("Usage:")
            print("  python start_autospace.py        - Start the server")
            print("  python start_autospace.py check  - Run system checks only")
            print("  python start_autospace.py test   - Test server connection")
            print("  python start_autospace.py help   - Show this help")
            sys.exit(0)
        
        else:
            print(f"Unknown command: {command}")
            print("Use 'python start_autospace.py help' for usage information")
            sys.exit(1)
    
    # Default: start the server
    success = start_server()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
