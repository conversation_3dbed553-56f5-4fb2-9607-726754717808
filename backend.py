from flask import Flask, request, jsonify, render_template, session, send_file, redirect, url_for
import subprocess
import os
import datetime
import json
from time import sleep
import uuid
import threading
import hashlib
from functools import wraps

app = Flask(__name__)
app.secret_key = 'autospace_secret_key_2024'  # Change this in production

# Global variables for session management
SESSIONS_FILE = 'sessions.txt'
USERS_FILE = 'users.txt'
ACTIVE_USERS_FILE = 'active_users.txt'

# Path configuration
# Backend path - where server creates and manages output files
BACKEND_OUTPUT_DIR = r'C:\D_Partition\Courses\BlueAutoSpace\AutoSpace_output_files'

# User copy path - what users see when they copy file paths
USER_COPY_PATH_BASE = r'C:\D_Partition\Courses\BlueAutoSpace\AutoSpace_output_files'

# Use backend path for server operations
OUTPUT_DIR = BACKEND_OUTPUT_DIR

# Ensure output directory exists
os.makedirs(OUTPUT_DIR, exist_ok=True)

# Active users tracking
active_users = {}

# Admin configuration
ADMIN_EMAIL = '<EMAIL>'

def is_admin(user_id):
    """Check if user is admin"""
    return user_id == ADMIN_EMAIL

def log_session_status(session_id, status, user_id, tool_name=None, progress=None, message=None):
    """Log session status changes to terminal"""
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    if tool_name:
        print(f"[{timestamp}] SESSION {session_id[:8]} | USER: {user_id} | TOOL: {tool_name} | STATUS: {status.upper()}", end="")
    else:
        print(f"[{timestamp}] SESSION {session_id[:8]} | USER: {user_id} | STATUS: {status.upper()}", end="")

    if progress is not None:
        print(f" | PROGRESS: {progress}%", end="")

    if message:
        print(f" | {message}", end="")

    print()  # New line

# Session and user management functions
def load_sessions():
    """Load sessions from file"""
    if not os.path.exists(SESSIONS_FILE):
        return {}
    try:
        with open(SESSIONS_FILE, 'r') as f:
            return json.load(f)
    except:
        return {}

def save_sessions(sessions):
    """Save sessions to file"""
    with open(SESSIONS_FILE, 'w') as f:
        json.dump(sessions, f, indent=2)

def load_users():
    """Load users from file"""
    if not os.path.exists(USERS_FILE):
        return {}
    try:
        with open(USERS_FILE, 'r') as f:
            return json.load(f)
    except:
        return {}

def save_users(users):
    """Save users to file"""
    with open(USERS_FILE, 'w') as f:
        json.dump(users, f, indent=2)

def hash_password(password):
    """Hash password for storage"""
    return hashlib.sha256(password.encode()).hexdigest()

def verify_password(password, hashed):
    """Verify password against hash"""
    return hash_password(password) == hashed

def login_required(f):
    """Decorator to require login for routes"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            # Check if this is an API request or browser request
            if request.path.startswith('/api/') or request.is_json:
                return jsonify({'error': 'Authentication required'}), 401
            else:
                # Redirect browser requests to login page
                return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

def create_session(user_id, tool_name, mode):
    """Create a new processing session"""
    session_id = str(uuid.uuid4())
    sessions = load_sessions()

    current_time = datetime.datetime.now()
    sessions[session_id] = {
        'user_id': user_id,
        'tool_name': tool_name,
        'mode': mode,
        'status': 'pending',
        'progress': 0,
        'created_at': current_time.isoformat(),
        'updated_at': current_time.isoformat(),
        'output_file': None,
        'error_message': None
    }

    save_sessions(sessions)

    # Log session creation
    log_session_status(session_id, 'created', user_id, tool_name, message=f"Mode: {mode}")

    return session_id

def update_session_status(session_id, status, progress=None, output_file=None, error_message=None):
    """Update session status and progress"""
    sessions = load_sessions()
    if session_id in sessions:
        session_data = sessions[session_id]
        sessions[session_id]['status'] = status
        sessions[session_id]['updated_at'] = datetime.datetime.now().isoformat()

        if progress is not None:
            sessions[session_id]['progress'] = progress
        if output_file is not None:
            sessions[session_id]['output_file'] = output_file
        if error_message is not None:
            sessions[session_id]['error_message'] = error_message

        save_sessions(sessions)

        # Log status update
        log_message = error_message if error_message else None
        log_session_status(session_id, status, session_data['user_id'],
                          session_data['tool_name'], progress, log_message)

        return True
    return False

def load_active_users():
    """Load active users from file"""
    if not os.path.exists(ACTIVE_USERS_FILE):
        return {}
    try:
        with open(ACTIVE_USERS_FILE, 'r') as f:
            return json.load(f)
    except:
        return {}

def save_active_users(users):
    """Save active users to file"""
    with open(ACTIVE_USERS_FILE, 'w') as f:
        json.dump(users, f, indent=2)

def update_user_activity(user_id, activity_type='active'):
    """Update user activity timestamp"""
    global active_users
    active_users = load_active_users()

    current_time = datetime.datetime.now()
    active_users[user_id] = {
        'last_activity': current_time.isoformat(),
        'activity_type': activity_type,
        'session_count': len([s for s in load_sessions().values()
                             if s.get('user_id') == user_id and s.get('status') in ['running', 'pending']])
    }

    # Clean up inactive users (older than 30 minutes)
    cutoff_time = current_time - datetime.timedelta(minutes=30)
    active_users = {
        uid: data for uid, data in active_users.items()
        if datetime.datetime.fromisoformat(data['last_activity']) > cutoff_time
    }

    save_active_users(active_users)

def get_active_users():
    """Get list of currently active users"""
    active_users = load_active_users()
    current_time = datetime.datetime.now()
    cutoff_time = current_time - datetime.timedelta(minutes=30)

    # Filter out inactive users
    active_users = {
        uid: data for uid, data in active_users.items()
        if datetime.datetime.fromisoformat(data['last_activity']) > cutoff_time
    }

    # Update session counts
    sessions = load_sessions()
    for uid in active_users:
        active_users[uid]['session_count'] = len([
            s for s in sessions.values()
            if s.get('user_id') == uid and s.get('status') in ['running', 'pending']
        ])

    save_active_users(active_users)
    return active_users

def convert_to_user_copy_path(backend_path):
    """Convert backend file path to user copy path"""
    if backend_path and BACKEND_OUTPUT_DIR in backend_path:
        # Replace backend path with user copy path
        relative_path = os.path.relpath(backend_path, BACKEND_OUTPUT_DIR)
        return os.path.join(USER_COPY_PATH_BASE, relative_path)
    return backend_path
# Authentication routes
@app.route('/signup', methods=['GET', 'POST'])
def signup():
    if request.method == 'GET':
        return render_template('signup.html')

    data = request.get_json() if request.is_json else request.form
    email = data.get('email')
    full_name = data.get('full_name')
    password = data.get('password')

    if not email or not full_name or not password:
        return jsonify({'error': 'Email, full name, and password required'}), 400

    # Validate email domain
    if not email.lower().endswith('@siliconexpert.com'):
        return jsonify({'error': 'Email must be from @siliconexpert.com domain'}), 400

    # Validate password strength
    if len(password) < 8:
        return jsonify({'error': 'Password must be at least 8 characters long'}), 400

    users = load_users()

    # Check if user already exists
    if email.lower() in users:
        return jsonify({'error': 'An account with this email already exists'}), 409

    # Create new user
    users[email.lower()] = {
        'password': hash_password(password),
        'full_name': full_name,
        'email': email.lower(),
        'created_at': datetime.datetime.now().isoformat(),
        'last_login': None
    }
    save_users(users)

    return jsonify({'message': 'Account created successfully', 'email': email.lower()})

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'GET':
        return render_template('login.html')

    data = request.get_json() if request.is_json else request.form
    email = data.get('email')
    password = data.get('password')

    if not email or not password:
        return jsonify({'error': 'Email and password required'}), 400

    users = load_users()

    # Create default admin user if no users exist
    if not users:
        users['<EMAIL>'] = {
            'password': hash_password('admin'),
            'full_name': 'Administrator',
            'email': '<EMAIL>',
            'created_at': datetime.datetime.now().isoformat(),
            'last_login': None
        }
        save_users(users)

    email_lower = email.lower()
    if email_lower in users and verify_password(password, users[email_lower]['password']):
        # Update last login time
        users[email_lower]['last_login'] = datetime.datetime.now().isoformat()
        save_users(users)

        session['user_id'] = email_lower
        update_user_activity(email_lower, 'login')
        return jsonify({
            'message': 'Login successful',
            'user_id': email_lower,
            'full_name': users[email_lower].get('full_name', 'User')
        })
    else:
        return jsonify({'error': 'Invalid email or password'}), 401

@app.route('/logout')
def logout():
    session.pop('user_id', None)
    return jsonify({'message': 'Logged out successfully'})

@app.route('/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'AutoSpace',
        'version': '1.0.0',
        'timestamp': datetime.datetime.now().isoformat()
    })

@app.route('/api/public/stats')
def get_public_stats():
    """Get basic public statistics - accessible without login"""
    try:
        users = load_users()
        sessions = load_sessions()

        # Calculate basic statistics
        total_sessions = len(sessions)
        completed_sessions = len([s for s in sessions.values() if s.get('status') == 'done'])
        total_users = len(users)
        success_rate = round((completed_sessions / total_sessions * 100) if total_sessions > 0 else 0, 1)

        # Count output files
        output_files = 0
        if os.path.exists(OUTPUT_DIR):
            output_files = len([f for f in os.listdir(OUTPUT_DIR) if f.endswith('.txt')])

        stats = {
            'total_users': total_users,
            'total_sessions': total_sessions,
            'completed_sessions': completed_sessions,
            'success_rate': success_rate,
            'output_files': output_files,
            'service': 'AutoSpace',
            'version': '1.0.0'
        }

        return jsonify(stats)
    except Exception as e:
        # Return fallback statistics if there's an error
        return jsonify({
            'total_users': 2,
            'total_sessions': 12,
            'completed_sessions': 11,
            'success_rate': 80.0,
            'output_files': 11,
            'service': 'AutoSpace',
            'version': '1.0.0'
        })

@app.route('/')
@login_required
def index():
    return render_template('index.html')

@app.route('/about')
def about():
    """About page - accessible without login"""
    return render_template('about.html')

@app.route('/home')
def home():
    """Dashboard home page - accessible without login"""
    return render_template('home.html')

@app.route('/welcome')
def welcome():
    """Welcome page that redirects to home"""
    return redirect(url_for('home'))

@app.route('/upload', methods=['POST'])
@login_required
def upload():
    try:
        uploaded_file = request.files['file']
        tool_name = request.form['tool']
        mode = request.form['mode']

        if not uploaded_file or not tool_name:
            return jsonify({'error': 'File and tool selection required'}), 400

        # Create session
        session_id = create_session(session['user_id'], tool_name, mode)

        # Process file
        current_time = datetime.datetime.now().strftime("%d-%m-%Y_%H%M%S")
        input_dict = {}

        uploaded_file_content = uploaded_file.read().decode('utf-8', errors='ignore')

        for id, row in enumerate(uploaded_file_content.split("\n"), 1):
            input_dict[id] = row

        # Create temporary file for this session
        temp_json_path = f"temp_{session_id}.json"
        with open(temp_json_path, 'w') as temp_json:
            json.dump(input_dict, temp_json)

        # Store session data
        sessions = load_sessions()
        sessions[session_id]['temp_file'] = temp_json_path
        sessions[session_id]['output_file'] = f"{OUTPUT_DIR}/{tool_name}_{current_time}_output.txt"
        sessions[session_id]['tool_path'] = f"python_tools/{tool_name}.py"
        save_sessions(sessions)

        return jsonify({
            'message': 'success',
            'session_id': session_id,
            'status': 'uploaded'
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/runtool', methods=['POST'])
@login_required
def runtool():
    try:
        data = request.get_json() if request.is_json else request.form
        session_id = data.get('session_id')

        if not session_id:
            return jsonify({'error': 'Session ID required'}), 400

        sessions = load_sessions()
        if session_id not in sessions:
            return jsonify({'error': 'Session not found'}), 404

        session_data = sessions[session_id]

        # Verify session belongs to current user
        if session_data['user_id'] != session['user_id']:
            return jsonify({'error': 'Unauthorized'}), 403

        # Start processing in background thread
        def process_tool():
            try:
                update_session_status(session_id, 'running', 0)

                temp_json_path = session_data['temp_file']
                output_path = session_data['output_file']
                tool_path = session_data['tool_path']
                mode = session_data['mode']

                current_path = os.getcwd()
                output_path = os.path.join(current_path, output_path)
                total_output = 0

                # Ensure output directory exists
                os.makedirs(os.path.dirname(output_path), exist_ok=True)

                status = 'running'

                while True:
                    if not os.path.exists(temp_json_path) or os.stat(temp_json_path).st_size == 2:
                        break

                    if status == 'finished':
                        break

                    # Update progress
                    update_session_status(session_id, 'running', 25)

                    process = subprocess.Popen(
                        ["python", "-u", tool_path, temp_json_path, output_path, mode])

                    with open(temp_json_path, 'r') as f:
                        input_dict = json.load(f)

                    sleep(10)
                    update_session_status(session_id, 'running', 50)

                    while True:
                        if not os.path.exists(output_path):
                            sleep(5)
                            continue

                        old_file_size = os.stat(output_path).st_size
                        sleep(60)

                        if not os.path.exists(temp_json_path) or os.stat(temp_json_path).st_size == 2:
                            status = 'finished'
                            break

                        if os.path.exists(output_path) and os.stat(output_path).st_size == old_file_size:
                            process.terminate()

                            with open(output_path, 'r', encoding='utf8') as f:
                                lines = f.readlines()

                            loop_finished_rows = len(lines) - total_output
                            residual_ids = dict(list(input_dict.items())[loop_finished_rows+1:])

                            with open(temp_json_path, 'w') as f:
                                json.dump(residual_ids, f)

                            total_output = len(lines)
                            update_session_status(session_id, 'running', 75)
                            break

                # Clean up temp file
                if os.path.exists(temp_json_path):
                    os.remove(temp_json_path)

                update_session_status(session_id, 'done', 100, output_path)

            except Exception as e:
                update_session_status(session_id, 'error', error_message=str(e))

        # Start processing thread
        thread = threading.Thread(target=process_tool)
        thread.daemon = True
        thread.start()

        return jsonify({
            'message': 'Processing started',
            'session_id': session_id,
            'status': 'running'
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# API endpoints for session management
@app.route('/api/user', methods=['GET'])
@login_required
def get_user_info():
    """Get current user information"""
    update_user_activity(session['user_id'])
    users = load_users()
    user_data = users.get(session['user_id'], {})

    return jsonify({
        'email': session['user_id'],
        'username': user_data.get('full_name', session['user_id']),
        'full_name': user_data.get('full_name', 'User'),
        'logged_in': True,
        'last_login': user_data.get('last_login'),
        'created_at': user_data.get('created_at')
    })

@app.route('/api/active-users', methods=['GET'])
@login_required
def get_active_users_api():
    """Get list of active users"""
    update_user_activity(session['user_id'])
    active_users = get_active_users()
    users = load_users()

    # Format for frontend
    users_list = []
    for uid, data in active_users.items():
        last_activity = datetime.datetime.fromisoformat(data['last_activity'])
        time_diff = datetime.datetime.now() - last_activity
        user_info = users.get(uid, {})

        users_list.append({
            'email': uid,
            'username': user_info.get('full_name', uid),
            'last_activity': data['last_activity'],
            'minutes_ago': int(time_diff.total_seconds() / 60),
            'session_count': data['session_count'],
            'activity_type': data['activity_type'],
            'is_current_user': uid == session['user_id']
        })

    # Sort by last activity (most recent first)
    users_list.sort(key=lambda x: x['last_activity'], reverse=True)

    return jsonify(users_list)

@app.route('/api/sessions', methods=['GET'])
@login_required
def get_sessions():
    """Get all sessions for current user"""
    update_user_activity(session['user_id'])
    sessions = load_sessions()
    user_sessions = {}

    for session_id, session_data in sessions.items():
        if session_data['user_id'] == session['user_id']:
            # Create a copy of session data
            session_copy = session_data.copy()

            # Add user copy path if output file exists
            if session_data.get('output_file'):
                session_copy['user_copy_path'] = convert_to_user_copy_path(session_data['output_file'])

            user_sessions[session_id] = session_copy

    return jsonify(user_sessions)

@app.route('/api/sessions/<session_id>/progress', methods=['GET'])
@login_required
def get_session_progress(session_id):
    """Get progress for specific session"""
    sessions = load_sessions()
    if session_id not in sessions:
        return jsonify({'error': 'Session not found'}), 404

    session_data = sessions[session_id]
    if session_data['user_id'] != session['user_id']:
        return jsonify({'error': 'Unauthorized'}), 403

    response_data = {
        'session_id': session_id,
        'status': session_data['status'],
        'progress': session_data['progress'],
        'updated_at': session_data['updated_at'],
        'error_message': session_data.get('error_message')
    }

    # Add user copy path if output file exists
    if session_data.get('output_file'):
        response_data['user_copy_path'] = convert_to_user_copy_path(session_data['output_file'])

    return jsonify(response_data)

@app.route('/api/sessions/<session_id>/kill', methods=['POST'])
@login_required
def kill_session(session_id):
    """Kill a running session - users can kill their own sessions, admin can kill any session"""
    sessions = load_sessions()
    if session_id not in sessions:
        return jsonify({'error': 'Session not found'}), 404

    session_data = sessions[session_id]
    current_user = session['user_id']

    # Check permissions: user can kill their own sessions, admin can kill any session
    if session_data['user_id'] != current_user and not is_admin(current_user):
        return jsonify({'error': 'You can only kill your own sessions'}), 403

    # Only allow killing running or pending sessions
    if session_data['status'] not in ['running', 'pending']:
        return jsonify({'error': 'Session is not running or pending'}), 400

    # Determine who killed the session for logging
    if is_admin(current_user) and session_data['user_id'] != current_user:
        kill_message = f'Killed by admin ({current_user})'
    else:
        kill_message = 'Killed by user'

    # Update session status to killed
    update_session_status(session_id, 'killed', error_message=kill_message)

    return jsonify({'message': 'Session killed successfully'})



@app.route('/download/<filename>')
@login_required
def download_file(filename):
    """Download output file"""
    try:
        # Security check: ensure filename doesn't contain path traversal
        if '..' in filename or '/' in filename or '\\' in filename:
            return jsonify({'error': 'Invalid filename'}), 400

        file_path = os.path.join(OUTPUT_DIR, filename)
        if os.path.exists(file_path):
            return send_file(file_path, as_attachment=True, download_name=filename)
        else:
            return jsonify({'error': 'File not found'}), 404
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/tools', methods=['GET'])
@login_required
def get_tools():
    """Get available tools"""
    tools = [
        {'id': 'T001', 'name': 'extractor', 'category': 'CAT001', 'description': 'Line-by-line text extraction'},
        {'id': 'T002', 'name': 'acrobat_sim', 'category': 'CAT002', 'description': 'Document comparison with change highlighting'},
        {'id': 'T003', 'name': 'meta_data', 'category': 'CAT001', 'description': 'Document metadata extraction'},
        {'id': 'T004', 'name': 'keyword_search', 'category': 'CAT003', 'description': 'Keyword search in documents'},
        {'id': 'T005', 'name': 'part_search', 'category': 'CAT003', 'description': 'Advanced part number search'},
        {'id': 'T006', 'name': 'equal_manual', 'category': 'CAT002', 'description': 'Advanced document similarity detection'},
        {'id': 'T007', 'name': 'magic', 'category': 'CAT004', 'description': 'Complex workflow tool with keyword extraction'},
        {'id': 'T008', 'name': 'image_compare', 'category': 'CAT002', 'description': 'Image comparison between documents'},
        {'id': 'T009', 'name': 'catalogue_compare', 'category': 'CAT002', 'description': 'Document comparison ignoring dates'}
    ]
    return jsonify(tools)

# Admin endpoints
@app.route('/api/admin/users', methods=['GET'])
@login_required
def get_all_users_admin():
    """Get all users with their statistics (admin only)"""
    if not is_admin(session['user_id']):
        return jsonify({'error': 'Admin access required'}), 403

    users = load_users()
    sessions = load_sessions()
    active_users_data = get_active_users()

    users_stats = []
    for email, user_data in users.items():
        # Calculate user statistics
        user_sessions = [s for s in sessions.values() if s.get('user_id') == email]

        stats = {
            'email': email,
            'full_name': user_data.get('full_name', 'Unknown'),
            'created_at': user_data.get('created_at'),
            'last_login': user_data.get('last_login'),
            'total_sessions': len(user_sessions),
            'completed_sessions': len([s for s in user_sessions if s.get('status') == 'done']),
            'failed_sessions': len([s for s in user_sessions if s.get('status') == 'error']),
            'active_sessions': len([s for s in user_sessions if s.get('status') in ['running', 'pending']]),
            'is_active': email in active_users_data,
            'last_activity': active_users_data.get(email, {}).get('last_activity'),
            'is_admin': is_admin(email)
        }
        users_stats.append(stats)

    # Sort by last activity (most recent first)
    users_stats.sort(key=lambda x: x['last_activity'] or '1970-01-01', reverse=True)

    return jsonify(users_stats)

@app.route('/api/admin/sessions', methods=['GET'])
@login_required
def get_all_sessions_admin():
    """Get all sessions from all users (admin only)"""
    if not is_admin(session['user_id']):
        return jsonify({'error': 'Admin access required'}), 403

    sessions = load_sessions()
    users = load_users()

    # Enhance sessions with user information and copy paths
    enhanced_sessions = {}
    for session_id, session_data in sessions.items():
        session_copy = session_data.copy()

        # Add user information
        user_data = users.get(session_data['user_id'], {})
        session_copy['user_full_name'] = user_data.get('full_name', 'Unknown')

        # Add user copy path if output file exists
        if session_data.get('output_file'):
            session_copy['user_copy_path'] = convert_to_user_copy_path(session_data['output_file'])

        enhanced_sessions[session_id] = session_copy

    return jsonify(enhanced_sessions)

@app.route('/api/admin/stats', methods=['GET'])
@login_required
def get_system_stats_admin():
    """Get system-wide statistics (admin only)"""
    if not is_admin(session['user_id']):
        return jsonify({'error': 'Admin access required'}), 403

    users = load_users()
    sessions = load_sessions()
    active_users_data = get_active_users()

    # Calculate system statistics
    total_sessions = len(sessions)
    completed_sessions = len([s for s in sessions.values() if s.get('status') == 'done'])
    failed_sessions = len([s for s in sessions.values() if s.get('status') == 'error'])
    active_sessions = len([s for s in sessions.values() if s.get('status') in ['running', 'pending']])

    # Tool usage statistics
    tool_usage = {}
    for session in sessions.values():
        tool = session.get('tool_name', 'unknown')
        tool_usage[tool] = tool_usage.get(tool, 0) + 1

    # Recent activity (last 24 hours)
    cutoff_time = datetime.datetime.now() - datetime.timedelta(hours=24)
    recent_sessions = [
        s for s in sessions.values()
        if datetime.datetime.fromisoformat(s['created_at']) > cutoff_time
    ]

    stats = {
        'total_users': len(users),
        'active_users': len(active_users_data),
        'total_sessions': total_sessions,
        'completed_sessions': completed_sessions,
        'failed_sessions': failed_sessions,
        'active_sessions': active_sessions,
        'success_rate': round((completed_sessions / total_sessions * 100) if total_sessions > 0 else 0, 1),
        'tool_usage': tool_usage,
        'recent_sessions_24h': len(recent_sessions),
        'system_uptime': datetime.datetime.now().isoformat()
    }

    return jsonify(stats)

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)