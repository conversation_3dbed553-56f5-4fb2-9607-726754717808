window.copyFilePath = function(sessionId) {
    fetch(`/api/sessions/${sessionId}/progress`)
        .then(response => response.json())
        .then(data => {
            if (data.shared_output_path) {
                navigator.clipboard.writeText(data.shared_output_path).then(() => {
                    showNotification('Shared network path copied to clipboard', 'success');
                });
            } else {
                showNotification('Output file not available yet', 'warning');
            }
        })
        .catch(error => {
            console.error('Error copying path:', error);
            showNotification('Failed to copy path', 'error');
        });
};