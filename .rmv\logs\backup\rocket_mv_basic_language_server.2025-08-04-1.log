2025-08-04 13:00:10.857 [main] INFO  com.rs.mv.mvvs.Startup - [LSP:100101]ROCKET MV BASIC EXTENSION to start
2025-08-04 13:00:10.872 [main] INFO  com.rs.mv.mvvs.lsp.server.BasicLanguageServerLauncher - [LSP:100102]start pip server
2025-08-04 13:00:10.957 [main] INFO  com.rs.mv.mvvs.lsp.server.BasicLanguageServerLauncher - [LSP:100103]ROCKET MV BASIC EXTENSION start successful
2025-08-04 13:00:11.003 [pool-3-thread-1] INFO  com.rs.mv.mvvs.instance.workspace.WorkspaceManager - [workspace:200101]load all workspace folders start
2025-08-04 13:00:11.004 [pool-3-thread-1] INFO  com.rs.mv.mvvs.instance.workspace.WorkspaceManager - [workspace:20010201]root uri: file:///c%3A/D_Partition/Courses/BlueAutoSpace
2025-08-04 13:00:11.005 [pool-3-thread-1] INFO  com.rs.mv.mvvs.instance.workspace.handler.FolderHandler - [workspace:200103]workspace contains workspace folder: file:///c%3A/D_Partition/Courses/BlueAutoSpace
2025-08-04 13:00:11.022 [pool-3-thread-1] INFO  com.rs.mv.mvvs.instance.workspace.handler.FolderHandler - [workspace:200104]workspace folder will be cached: file:///c%3A/D_Partition/Courses/BlueAutoSpace
2025-08-04 13:00:11.023 [pool-3-thread-1] INFO  com.rs.mv.mvvs.rmv.config.Config - [framework:30010101]config load start
2025-08-04 13:00:11.023 [pool-3-thread-1] INFO  com.rs.mv.mvvs.rmv.config.Config - [framework:30010102]project config path is : C:/D_Partition/Courses/BlueAutoSpace/.rmv/config
2025-08-04 13:00:11.024 [pool-3-thread-1] INFO  com.rs.mv.mvvs.rmv.config.Config - [framework:30010103]resource config path is : /
2025-08-04 13:00:11.024 [pool-3-thread-1] INFO  com.rs.mv.mvvs.rmv.config.Config - [framework:30010104]user config path is : C:/Users/<USER>
2025-08-04 13:00:11.048 [pool-3-thread-1] INFO  com.rs.mv.mvvs.rmv.config.Config - [framework:30010105]config load end
2025-08-04 13:00:11.049 [pool-3-thread-1] INFO  com.rs.mv.mvvs.rmv.documentation.BasicDocumentation - [framework:30020101]documentation load start
2025-08-04 13:00:11.050 [pool-3-thread-1] INFO  com.rs.mv.mvvs.rmv.documentation.reader.SystemDocReader - [framework:30020102]system documentation path: /documentation/UNIVERSE/
2025-08-04 13:00:11.054 [pool-3-thread-1] INFO  com.rs.mv.mvvs.rmv.documentation.reader.ProjectDocReader - [framework:30020103]project documentation brief file: C:\D_Partition\Courses\BlueAutoSpace\.rmv\documentation\UNIVERSE\custom.json
2025-08-04 13:00:11.056 [pool-3-thread-1] INFO  com.rs.mv.mvvs.rmv.documentation.reader.UserDocReader - [framework:********]user documentation brief file: C:\Users\<USER>\documentation\UNIVERSE\custom.json
2025-08-04 13:00:11.128 [pool-3-thread-1] INFO  com.rs.mv.mvvs.rmv.documentation.BasicDocumentation - [framework:********]documentation load end
2025-08-04 13:00:11.133 [pool-3-thread-1] INFO  com.rs.mv.mvvs.common.convertor.ConvertorManager - [framework:********]convertors for folder(C:/D_Partition/Courses/BlueAutoSpace) load start
2025-08-04 13:00:11.140 [pool-3-thread-1] INFO  com.rs.mv.mvvs.common.convertor.ConvertorManager - [framework:********]comment convertor load finish
2025-08-04 13:00:11.316 [pool-3-thread-1] INFO  com.rs.mv.mvvs.common.convertor.ConvertorManager - [framework:30030103]keyword convertor load finish
2025-08-04 13:00:11.318 [pool-3-thread-1] INFO  com.rs.mv.mvvs.common.convertor.ConvertorManager - [framework:30030104]operator convertor load finish
2025-08-04 13:00:11.318 [pool-3-thread-1] INFO  com.rs.mv.mvvs.common.convertor.ConvertorManager - [framework:30030105]function name convertor load finish
2025-08-04 13:00:11.318 [pool-3-thread-1] INFO  com.rs.mv.mvvs.common.convertor.ConvertorManager - [framework:30030106]atVariable convertor load finish
2025-08-04 13:00:11.318 [pool-3-thread-1] INFO  com.rs.mv.mvvs.common.convertor.ConvertorManager - [framework:30030107]convertors for folder(C:/D_Partition/Courses/BlueAutoSpace) load end
2025-08-04 13:00:11.320 [pool-3-thread-1] INFO  com.rs.mv.mvvs.rmv.config.CapabilityConfig - [framework:********]capabilities file(C:/D_Partition/Courses/BlueAutoSpace/config/capability) doesn't exist or read error, default config will take effect
2025-08-04 13:00:11.321 [pool-3-thread-1] INFO  com.rs.mv.mvvs.instance.workspace.WorkspaceManager - [workspace:200105]load all workspace folders successful
2025-08-04 13:00:11.323 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.account.AccountManager - [client:********]load account manager start
2025-08-04 13:00:11.333 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.account.handler.CacheAccountHandler - [client:********]load cache account: 
2025-08-04 13:00:11.335 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.account.handler.ExternalAccountHandler - [client:********]load external account: 
2025-08-04 13:00:11.366 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.manager.U2CatalogManager - [client:400203]cache file auto update thread launch in BlueAutoSpace account
2025-08-04 13:00:11.374 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.from.config.DirsConfigReader - [client:400206]parse dir config information
2025-08-04 13:00:11.379 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.from.config.MappingConfigReader - [client:400207]parse mapping config information
2025-08-04 13:00:11.380 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.manager.U2CatalogManager - [client:400202]start a schedule task to get global/local catalog for BlueAutoSpace, its period 5s 
2025-08-04 13:00:11.381 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.manager.U2CatalogManager - [client:400203]cache file auto update thread launch in .git account
2025-08-04 13:00:11.381 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.from.config.DirsConfigReader - [client:400206]parse dir config information
2025-08-04 13:00:11.382 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.from.config.MappingConfigReader - [client:400207]parse mapping config information
2025-08-04 13:00:11.382 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.manager.U2CatalogManager - [client:400202]start a schedule task to get global/local catalog for .git, its period 5s 
2025-08-04 13:00:11.383 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.manager.U2CatalogManager - [client:400203]cache file auto update thread launch in logs account
2025-08-04 13:00:11.383 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.from.config.DirsConfigReader - [client:400206]parse dir config information
2025-08-04 13:00:11.383 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.from.config.MappingConfigReader - [client:400207]parse mapping config information
2025-08-04 13:00:11.384 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.manager.U2CatalogManager - [client:400202]start a schedule task to get global/local catalog for logs, its period 5s 
2025-08-04 13:00:11.385 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.manager.U2CatalogManager - [client:400203]cache file auto update thread launch in refs account
2025-08-04 13:00:11.386 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.from.config.DirsConfigReader - [client:400206]parse dir config information
2025-08-04 13:00:11.387 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.from.config.MappingConfigReader - [client:400207]parse mapping config information
2025-08-04 13:00:11.388 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.manager.U2CatalogManager - [client:400202]start a schedule task to get global/local catalog for refs, its period 5s 
2025-08-04 13:00:11.390 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.manager.U2CatalogManager - [client:400203]cache file auto update thread launch in objects account
2025-08-04 13:00:11.390 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.from.config.DirsConfigReader - [client:400206]parse dir config information
2025-08-04 13:00:11.391 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.from.config.MappingConfigReader - [client:400207]parse mapping config information
2025-08-04 13:00:11.391 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.manager.U2CatalogManager - [client:400202]start a schedule task to get global/local catalog for objects, its period 5s 
2025-08-04 13:00:11.391 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.manager.U2CatalogManager - [client:400203]cache file auto update thread launch in refs account
2025-08-04 13:00:11.392 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.from.config.DirsConfigReader - [client:400206]parse dir config information
2025-08-04 13:00:11.392 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.from.config.MappingConfigReader - [client:400207]parse mapping config information
2025-08-04 13:00:11.393 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.manager.U2CatalogManager - [client:400202]start a schedule task to get global/local catalog for refs, its period 5s 
2025-08-04 13:00:11.396 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.account.handler.ServerAccountHandler - [client:********]load server account: Account{name=BlueAutoSpace, path=C:/D_Partition/Courses/BlueAutoSpace, type=PREDICT}
Account{name=refs, path=C:/D_Partition/Courses/BlueAutoSpace/.git/logs/refs, type=PREDICT}
Account{name=.git, path=C:/D_Partition/Courses/BlueAutoSpace/.git, type=PREDICT}
Account{name=objects, path=C:/D_Partition/Courses/BlueAutoSpace/.git/objects, type=PREDICT}
Account{name=logs, path=C:/D_Partition/Courses/BlueAutoSpace/.git/logs, type=PREDICT}
Account{name=refs, path=C:/D_Partition/Courses/BlueAutoSpace/.git/refs, type=PREDICT}
2025-08-04 13:00:11.397 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.account.AccountManager - [client:********]load account manager finish
2025-08-04 13:00:11.398 [pool-3-thread-1] INFO  com.rs.mv.mvvs.instance.userspace.UserspaceManager - [framework:********]config file(C:\Users\<USER>\D_Partition\Courses\BlueAutoSpace\.rmv\documentation\UNIVERSE\custom.json
2025-08-04 15:40:15.053 [pool-3-thread-1] INFO  com.rs.mv.mvvs.rmv.documentation.reader.UserDocReader - [framework:********]user documentation brief file: C:\Users\<USER>\documentation\UNIVERSE\custom.json
2025-08-04 15:40:15.205 [pool-3-thread-1] INFO  com.rs.mv.mvvs.rmv.documentation.BasicDocumentation - [framework:********]documentation load end
2025-08-04 15:40:15.218 [pool-3-thread-1] INFO  com.rs.mv.mvvs.common.convertor.ConvertorManager - [framework:********]convertors for folder(C:/D_Partition/Courses/BlueAutoSpace) load start
2025-08-04 15:40:15.233 [pool-3-thread-1] INFO  com.rs.mv.mvvs.common.convertor.ConvertorManager - [framework:********]comment convertor load finish
2025-08-04 15:40:15.560 [pool-3-thread-1] INFO  com.rs.mv.mvvs.common.convertor.ConvertorManager - [framework:30030103]keyword convertor load finish
2025-08-04 15:40:15.563 [pool-3-thread-1] INFO  com.rs.mv.mvvs.common.convertor.ConvertorManager - [framework:30030104]operator convertor load finish
2025-08-04 15:40:15.563 [pool-3-thread-1] INFO  com.rs.mv.mvvs.common.convertor.ConvertorManager - [framework:30030105]function name convertor load finish
2025-08-04 15:40:15.564 [pool-3-thread-1] INFO  com.rs.mv.mvvs.common.convertor.ConvertorManager - [framework:30030106]atVariable convertor load finish
2025-08-04 15:40:15.564 [pool-3-thread-1] INFO  com.rs.mv.mvvs.common.convertor.ConvertorManager - [framework:30030107]convertors for folder(C:/D_Partition/Courses/BlueAutoSpace) load end
2025-08-04 15:40:15.567 [pool-3-thread-1] INFO  com.rs.mv.mvvs.rmv.config.CapabilityConfig - [framework:********]capabilities file(C:/D_Partition/Courses/BlueAutoSpace/config/capability) doesn't exist or read error, default config will take effect
2025-08-04 15:40:15.568 [pool-3-thread-1] INFO  com.rs.mv.mvvs.instance.workspace.WorkspaceManager - [workspace:200105]load all workspace folders successful
2025-08-04 15:40:15.570 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.account.AccountManager - [client:********]load account manager start
2025-08-04 15:40:15.586 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.account.handler.CacheAccountHandler - [client:********]load cache account: 
2025-08-04 15:40:15.588 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.account.handler.ExternalAccountHandler - [client:********]load external account: 
2025-08-04 15:40:15.654 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.manager.U2CatalogManager - [client:400203]cache file auto update thread launch in BlueAutoSpace account
2025-08-04 15:40:15.658 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.from.config.DirsConfigReader - [client:400206]parse dir config information
2025-08-04 15:40:15.659 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.from.config.MappingConfigReader - [client:400207]parse mapping config information
2025-08-04 15:40:15.661 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.manager.U2CatalogManager - [client:400202]start a schedule task to get global/local catalog for BlueAutoSpace, its period 5s 
2025-08-04 15:40:15.662 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.manager.U2CatalogManager - [client:400203]cache file auto update thread launch in .git account
2025-08-04 15:40:15.662 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.from.config.DirsConfigReader - [client:400206]parse dir config information
2025-08-04 15:40:15.662 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.from.config.MappingConfigReader - [client:400207]parse mapping config information
2025-08-04 15:40:15.662 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.manager.U2CatalogManager - [client:400202]start a schedule task to get global/local catalog for .git, its period 5s 
2025-08-04 15:40:15.663 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.manager.U2CatalogManager - [client:400203]cache file auto update thread launch in logs account
2025-08-04 15:40:15.663 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.from.config.DirsConfigReader - [client:400206]parse dir config information
2025-08-04 15:40:15.663 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.from.config.MappingConfigReader - [client:400207]parse mapping config information
2025-08-04 15:40:15.663 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.manager.U2CatalogManager - [client:400202]start a schedule task to get global/local catalog for logs, its period 5s 
2025-08-04 15:40:15.663 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.manager.U2CatalogManager - [client:400203]cache file auto update thread launch in refs account
2025-08-04 15:40:15.664 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.from.config.DirsConfigReader - [client:400206]parse dir config information
2025-08-04 15:40:15.666 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.from.config.MappingConfigReader - [client:400207]parse mapping config information
2025-08-04 15:40:15.667 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.manager.U2CatalogManager - [client:400202]start a schedule task to get global/local catalog for refs, its period 5s 
2025-08-04 15:40:15.669 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.manager.U2CatalogManager - [client:400203]cache file auto update thread launch in objects account
2025-08-04 15:40:15.670 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.from.config.DirsConfigReader - [client:400206]parse dir config information
2025-08-04 15:40:15.671 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.from.config.MappingConfigReader - [client:400207]parse mapping config information
2025-08-04 15:40:15.672 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.manager.U2CatalogManager - [client:400202]start a schedule task to get global/local catalog for objects, its period 5s 
2025-08-04 15:40:15.674 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.manager.U2CatalogManager - [client:400203]cache file auto update thread launch in refs account
2025-08-04 15:40:15.674 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.from.config.DirsConfigReader - [client:400206]parse dir config information
2025-08-04 15:40:15.674 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.from.config.MappingConfigReader - [client:400207]parse mapping config information
2025-08-04 15:40:15.674 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.manager.U2CatalogManager - [client:400202]start a schedule task to get global/local catalog for refs, its period 5s 
2025-08-04 15:40:15.678 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.account.handler.ServerAccountHandler - [client:********]load server account: Account{name=BlueAutoSpace, path=C:/D_Partition/Courses/BlueAutoSpace, type=PREDICT}
Account{name=refs, path=C:/D_Partition/Courses/BlueAutoSpace/.git/logs/refs, type=PREDICT}
Account{name=.git, path=C:/D_Partition/Courses/BlueAutoSpace/.git, type=PREDICT}
Account{name=objects, path=C:/D_Partition/Courses/BlueAutoSpace/.git/objects, type=PREDICT}
Account{name=logs, path=C:/D_Partition/Courses/BlueAutoSpace/.git/logs, type=PREDICT}
Account{name=refs, path=C:/D_Partition/Courses/BlueAutoSpace/.git/refs, type=PREDICT}
2025-08-04 15:40:15.678 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.account.AccountManager - [client:********]load account manager finish
2025-08-04 15:40:15.678 [pool-3-thread-1] INFO  com.rs.mv.mvvs.instance.userspace.UserspaceManager - [framework:********]config file(C:\Users\<USER>\D_Partition\Courses\BlueAutoSpace\.rmv\documentation\UNIVERSE\custom.json
2025-08-04 17:51:04.921 [pool-3-thread-1] INFO  com.rs.mv.mvvs.rmv.documentation.reader.UserDocReader - [framework:********]user documentation brief file: C:\Users\<USER>\documentation\UNIVERSE\custom.json
2025-08-04 17:51:05.094 [pool-3-thread-1] INFO  com.rs.mv.mvvs.rmv.documentation.BasicDocumentation - [framework:********]documentation load end
2025-08-04 17:51:05.108 [pool-3-thread-1] INFO  com.rs.mv.mvvs.common.convertor.ConvertorManager - [framework:********]convertors for folder(C:/D_Partition/Courses/BlueAutoSpace) load start
2025-08-04 17:51:05.118 [pool-3-thread-1] INFO  com.rs.mv.mvvs.common.convertor.ConvertorManager - [framework:********]comment convertor load finish
2025-08-04 17:51:05.455 [pool-3-thread-1] INFO  com.rs.mv.mvvs.common.convertor.ConvertorManager - [framework:30030103]keyword convertor load finish
2025-08-04 17:51:05.463 [pool-3-thread-1] INFO  com.rs.mv.mvvs.common.convertor.ConvertorManager - [framework:30030104]operator convertor load finish
2025-08-04 17:51:05.467 [pool-3-thread-1] INFO  com.rs.mv.mvvs.common.convertor.ConvertorManager - [framework:30030105]function name convertor load finish
2025-08-04 17:51:05.468 [pool-3-thread-1] INFO  com.rs.mv.mvvs.common.convertor.ConvertorManager - [framework:30030106]atVariable convertor load finish
2025-08-04 17:51:05.469 [pool-3-thread-1] INFO  com.rs.mv.mvvs.common.convertor.ConvertorManager - [framework:30030107]convertors for folder(C:/D_Partition/Courses/BlueAutoSpace) load end
2025-08-04 17:51:05.471 [pool-3-thread-1] INFO  com.rs.mv.mvvs.rmv.config.CapabilityConfig - [framework:********]capabilities file(C:/D_Partition/Courses/BlueAutoSpace/config/capability) doesn't exist or read error, default config will take effect
2025-08-04 17:51:05.472 [pool-3-thread-1] INFO  com.rs.mv.mvvs.instance.workspace.WorkspaceManager - [workspace:200105]load all workspace folders successful
2025-08-04 17:51:05.474 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.account.AccountManager - [client:********]load account manager start
2025-08-04 17:51:05.500 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.account.handler.CacheAccountHandler - [client:********]load cache account: 
2025-08-04 17:51:05.502 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.account.handler.ExternalAccountHandler - [client:********]load external account: 
2025-08-04 17:51:05.584 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.manager.U2CatalogManager - [client:400203]cache file auto update thread launch in BlueAutoSpace account
2025-08-04 17:51:05.588 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.from.config.DirsConfigReader - [client:400206]parse dir config information
2025-08-04 17:51:05.591 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.from.config.MappingConfigReader - [client:400207]parse mapping config information
2025-08-04 17:51:05.598 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.manager.U2CatalogManager - [client:400202]start a schedule task to get global/local catalog for BlueAutoSpace, its period 5s 
2025-08-04 17:51:05.600 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.manager.U2CatalogManager - [client:400203]cache file auto update thread launch in .git account
2025-08-04 17:51:05.600 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.from.config.DirsConfigReader - [client:400206]parse dir config information
2025-08-04 17:51:05.600 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.from.config.MappingConfigReader - [client:400207]parse mapping config information
2025-08-04 17:51:05.601 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.manager.U2CatalogManager - [client:400202]start a schedule task to get global/local catalog for .git, its period 5s 
2025-08-04 17:51:05.601 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.manager.U2CatalogManager - [client:400203]cache file auto update thread launch in logs account
2025-08-04 17:51:05.601 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.from.config.DirsConfigReader - [client:400206]parse dir config information
2025-08-04 17:51:05.602 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.from.config.MappingConfigReader - [client:400207]parse mapping config information
2025-08-04 17:51:05.602 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.manager.U2CatalogManager - [client:400202]start a schedule task to get global/local catalog for logs, its period 5s 
2025-08-04 17:51:05.602 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.manager.U2CatalogManager - [client:400203]cache file auto update thread launch in refs account
2025-08-04 17:51:05.602 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.from.config.DirsConfigReader - [client:400206]parse dir config information
2025-08-04 17:51:05.603 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.from.config.MappingConfigReader - [client:400207]parse mapping config information
2025-08-04 17:51:05.603 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.manager.U2CatalogManager - [client:400202]start a schedule task to get global/local catalog for refs, its period 5s 
2025-08-04 17:51:05.604 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.manager.U2CatalogManager - [client:400203]cache file auto update thread launch in objects account
2025-08-04 17:51:05.604 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.from.config.DirsConfigReader - [client:400206]parse dir config information
2025-08-04 17:51:05.604 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.from.config.MappingConfigReader - [client:400207]parse mapping config information
2025-08-04 17:51:05.605 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.manager.U2CatalogManager - [client:400202]start a schedule task to get global/local catalog for objects, its period 5s 
2025-08-04 17:51:05.605 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.manager.U2CatalogManager - [client:400203]cache file auto update thread launch in refs account
2025-08-04 17:51:05.607 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.from.config.DirsConfigReader - [client:400206]parse dir config information
2025-08-04 17:51:05.607 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.from.config.MappingConfigReader - [client:400207]parse mapping config information
2025-08-04 17:51:05.607 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.catalog.manager.U2CatalogManager - [client:400202]start a schedule task to get global/local catalog for refs, its period 5s 
2025-08-04 17:51:05.610 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.account.handler.ServerAccountHandler - [client:********]load server account: Account{name=BlueAutoSpace, path=C:/D_Partition/Courses/BlueAutoSpace, type=PREDICT}
Account{name=refs, path=C:/D_Partition/Courses/BlueAutoSpace/.git/logs/refs, type=PREDICT}
Account{name=.git, path=C:/D_Partition/Courses/BlueAutoSpace/.git, type=PREDICT}
Account{name=objects, path=C:/D_Partition/Courses/BlueAutoSpace/.git/objects, type=PREDICT}
Account{name=logs, path=C:/D_Partition/Courses/BlueAutoSpace/.git/logs, type=PREDICT}
Account{name=refs, path=C:/D_Partition/Courses/BlueAutoSpace/.git/refs, type=PREDICT}
2025-08-04 17:51:05.610 [pool-3-thread-1] INFO  com.rs.mv.mvvs.client.account.AccountManager - [client:********]load account manager finish
2025-08-04 17:51:05.610 [pool-3-thread-1] INFO  com.rs.mv.mvvs.instance.userspace.UserspaceManager - [framework:********]config file(C:\Users\<USER>