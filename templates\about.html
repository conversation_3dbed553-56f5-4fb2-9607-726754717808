<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>About - AutoSpace</title>
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;600;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
  <style>
    :root {
      --color-bg: #f5f5f5;
      --color-text: #2a2a2a;
      --color-text-secondary: #666;
      --color-white: #fff;
      --color-primary: #667eea;
      --color-accent: #764ba2;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Montserrat', sans-serif;
      background-color: var(--color-bg);
      color: var(--color-text);
      line-height: 1.6;
    }

    /* Navigation */
    .nav-header {
      background: var(--color-white);
      padding: 1rem 2rem;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 1000;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .nav-logo {
      font-size: 1.5rem;
      font-weight: 700;
      color: var(--color-primary);
      text-decoration: none;
      letter-spacing: 2px;
    }

    .nav-links {
      display: flex;
      list-style: none;
      gap: 2rem;
    }

    .nav-links a {
      text-decoration: none;
      color: var(--color-text);
      font-weight: 500;
      transition: color 0.3s ease;
    }

    .nav-links a:hover {
      color: var(--color-primary);
    }

    .cta-button {
      background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
      color: white;
      padding: 0.75rem 1.5rem;
      border-radius: 25px;
      text-decoration: none;
      font-weight: 600;
      transition: transform 0.3s ease;
    }

    .cta-button:hover {
      transform: translateY(-2px);
    }

    /* Main Content */
    .main-content {
      margin-top: 80px;
    }

    .title {
      width: 100%;
      max-width: 500px;
      font-size: 4rem;
      font-weight: 700;
      line-height: 1.1;
      color: var(--color-text);
      margin-bottom: 2rem;
    }

    .stack-area {
      width: 100%;
      height: 500vh;
      background: var(--color-bg);
      display: flex;
    }

    .left, .right {
      height: 100vh;
      flex-basis: 50%;
      top: 80px;
      position: sticky;
    }

    .left {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 2rem;
      box-sizing: border-box;
    }

    .sub-title {
      width: 100%;
      max-width: 500px;
      margin-top: 2rem;
      font-size: 1.1rem;
      color: var(--color-text-secondary);
      line-height: 1.8;
    }

    .stats-summary {
      background: var(--color-white);
      padding: 2rem;
      border-radius: 15px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.1);
      margin-top: 2rem;
      width: 100%;
      max-width: 500px;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 1rem;
      margin-top: 1rem;
    }

    .stat-item {
      text-align: center;
      padding: 1rem;
      background: linear-gradient(135deg, #f8f9ff, #e8f0ff);
      border-radius: 10px;
    }

    .stat-number {
      font-size: 2rem;
      font-weight: 700;
      color: var(--color-primary);
      display: block;
    }

    .stat-label {
      font-size: 0.9rem;
      color: var(--color-text-secondary);
      text-transform: uppercase;
      letter-spacing: 1px;
    }

    .cta-button-main {
      font-size: 1rem;
      border: none;
      border-radius: 25px;
      padding: 1rem 2rem;
      background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
      color: white;
      cursor: pointer;
      margin-top: 2rem;
      font-weight: 600;
      transition: transform 0.3s ease;
      text-decoration: none;
      display: inline-block;
    }

    .cta-button-main:hover {
      transform: translateY(-2px);
    }

    .card-element {
      width: 350px;
      height: 350px;
      border-radius: 25px;
      margin-bottom: 10px;
      position: absolute;
      top: calc(50% - 175px);
      left: calc(50% - 175px);
      transition: 0.5s ease-in-out;
      box-sizing: border-box;
      padding: 35px;
      display: flex;
      justify-content: space-between;
      flex-direction: column;
      color: white;
      box-shadow: 0 20px 40px rgba(0,0,0,0.2);
    }

    .card-element:hover {
      filter: brightness(110%);
    }

    .card-sub {
      font-size: 1.2rem;
      font-weight: 600;
      opacity: 0.9;
      text-transform: uppercase;
      letter-spacing: 1px;
    }

    .card-content {
      font-size: 2.5rem;
      font-weight: 700;
      line-height: 1.2;
    }

    .card-element:nth-child(1) {
      background: linear-gradient(135deg, #667eea, #764ba2);
    }
    .card-element:nth-child(2) {
      background: linear-gradient(135deg, #f093fb, #f5576c);
    }
    .card-element:nth-child(3) {
      background: linear-gradient(135deg, #4facfe, #00f2fe);
    }
    .card-element:nth-child(4) {
      background: linear-gradient(135deg, #43e97b, #38f9d7);
    }

    .away {
      transform-origin: bottom left;
    }

    /* Footer */
    .footer {
      background: var(--color-white);
      padding: 3rem 2rem;
      text-align: center;
      box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
    }

    .footer-content {
      max-width: 1200px;
      margin: 0 auto;
    }

    .footer-title {
      font-size: 2rem;
      font-weight: 700;
      color: var(--color-primary);
      margin-bottom: 1rem;
    }

    .footer-description {
      color: var(--color-text-secondary);
      margin-bottom: 2rem;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
    }

    .footer-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 2rem;
      margin: 2rem 0;
    }

    .footer-stat {
      padding: 1.5rem;
      background: linear-gradient(135deg, #f8f9ff, #e8f0ff);
      border-radius: 15px;
    }

    .footer-stat-number {
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--color-primary);
      display: block;
    }

    .footer-stat-label {
      color: var(--color-text-secondary);
      font-size: 0.9rem;
      text-transform: uppercase;
      letter-spacing: 1px;
    }

    .copyright {
      margin-top: 2rem;
      padding-top: 2rem;
      border-top: 1px solid #e0e0e0;
      color: var(--color-text-secondary);
      font-size: 0.9rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .stack-area {
        flex-direction: column;
        height: auto;
      }
      
      .left, .right {
        position: static;
        height: auto;
        flex-basis: auto;
      }
      
      .title {
        font-size: 2.5rem;
      }
      
      .card-element {
        position: static;
        margin: 1rem auto;
        width: 90%;
        max-width: 350px;
      }
      
      .nav-links {
        display: none;
      }
    }
  </style>
</head>
<body>
  <!-- Navigation -->
  <nav class="nav-header">
    <a href="/home" class="nav-logo">AUTOSPACE</a>
    <ul class="nav-links">
      <li><a href="/home">Dashboard</a></li>
      <li><a href="/about">About</a></li>
      <li><a href="/">Application</a></li>
    </ul>
    <a href="/login" class="cta-button">Get Started</a>
  </nav>

  <!-- Main Content -->
  <div class="main-content">
    <div class="stack-area">
      <div class="left">
        <div class="title">
          Auto Space
        </div>
        <div class="sub-title">
          Revolutionizing document processing with intelligent automation.
          AutoSpace transforms complex document workflows into simple, efficient processes
          that save time and boost productivity across your organization.

          <div class="stats-summary">
            <h3 style="margin-bottom: 1rem; color: var(--color-primary);">
              <i class="fas fa-chart-line"></i> Live Statistics
            </h3>
            <div class="stats-grid">
              <div class="stat-item">
                <span class="stat-number" id="total-sessions">12</span>
                <span class="stat-label">Total Sessions</span>
              </div>
              <div class="stat-item">
                <span class="stat-number" id="total-users">2</span>
                <span class="stat-label">Registered Users</span>
              </div>
              <div class="stat-item">
                <span class="stat-number" id="success-rate">80%</span>
                <span class="stat-label">Success Rate</span>
              </div>
              <div class="stat-item">
                <span class="stat-number" id="processed-files">11</span>
                <span class="stat-label">Files Processed</span>
              </div>
            </div>
          </div>

          <a href="/login" class="cta-button-main">Start Processing Documents</a>
        </div>
      </div>

      <div class="right">
        <div class="card-element">
          <div class="card-sub">Intelligent</div>
          <div class="card-content">Advanced AI-powered document extraction</div>
        </div>
        <div class="card-element">
          <div class="card-sub">Efficient</div>
          <div class="card-content">Process documents 10x faster than manual methods</div>
        </div>
        <div class="card-element">
          <div class="card-sub">Versatile</div>
          <div class="card-content">9 specialized tools for every document need</div>
        </div>
        <div class="card-element">
          <div class="card-sub">Reliable</div>
          <div class="card-content">Enterprise-grade security and session management</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer with detailed statistics -->
  <footer class="footer">
    <div class="footer-content">
      <h2 class="footer-title">
        <i class="fas fa-rocket"></i> Powering Document Processing Excellence
      </h2>
      <p class="footer-description">
        AutoSpace provides a comprehensive suite of document processing tools designed for modern businesses.
        From text extraction to image comparison, our platform handles complex document workflows with ease.
      </p>

      <div class="footer-stats">
        <div class="footer-stat">
          <span class="footer-stat-number">9</span>
          <span class="footer-stat-label">Processing Tools</span>
        </div>
        <div class="footer-stat">
          <span class="footer-stat-number" id="footer-sessions">12</span>
          <span class="footer-stat-label">Total Sessions</span>
        </div>
        <div class="footer-stat">
          <span class="footer-stat-number" id="footer-files">11</span>
          <span class="footer-stat-label">Documents Processed</span>
        </div>
        <div class="footer-stat">
          <span class="footer-stat-number">24/7</span>
          <span class="footer-stat-label">System Availability</span>
        </div>
      </div>

      <div style="margin: 2rem 0; padding: 2rem; background: linear-gradient(135deg, #f8f9ff, #e8f0ff); border-radius: 15px;">
        <h3 style="color: var(--color-primary); margin-bottom: 1rem;">
          <i class="fas fa-tools"></i> Available Tools
        </h3>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; text-align: left;">
          <div>
            <strong>Extraction Tools:</strong><br>
            • Text Extractor<br>
            • Metadata Extractor<br>
            • Magic Workflow Tool
          </div>
          <div>
            <strong>Comparison Tools:</strong><br>
            • Document Comparison<br>
            • Image Comparison<br>
            • Catalogue Comparison
          </div>
          <div>
            <strong>Search & Analysis:</strong><br>
            • Keyword Search<br>
            • Part Search<br>
            • Acrobat Simulator
          </div>
        </div>
      </div>

      <div class="copyright">
        © 2025 AutoSpace - <NAME_EMAIL>. All rights reserved.
      </div>
    </div>
  </footer>

  <script>
    // Stacked cards animation
    const cards = document.querySelectorAll('.card-element');
    const stackArea = document.querySelector(".stack-area");

    function rotateCards() {
      let angle = 0;
      cards.forEach((card, index) => {
        if (card.classList.contains("away")) {
          card.style.transform = `translateY(-150vh) rotate(-120deg)`;
        } else {
          card.style.transform = `rotate(${angle}deg)`;
          angle = angle - 10;
          card.style.zIndex = cards.length - index;
        }
      });
    }

    rotateCards();

    window.addEventListener("scroll", () => {
      let distance = window.innerHeight;
      let topVal = stackArea.getBoundingClientRect().top;
      let index = -1 * (topVal / distance + 1);
      index = Math.floor(index);

      for (let i = 0; i < cards.length; i++) {
        if (i <= index) {
          cards[i].classList.add("away");
        } else {
          cards[i].classList.remove("away");
        }
      }
      rotateCards();
    });

    // Load real-time statistics
    async function loadStatistics() {
      try {
        // First try the public stats endpoint
        const response = await fetch('/api/public/stats');
        if (response.ok) {
          const stats = await response.json();

          // Update with real statistics
          document.getElementById('total-sessions').textContent = stats.total_sessions || 0;
          document.getElementById('total-users').textContent = stats.total_users || 0;
          document.getElementById('success-rate').textContent = stats.success_rate + '%' || '0%';
          document.getElementById('processed-files').textContent = stats.completed_sessions || 0;

          // Update footer stats
          document.getElementById('footer-sessions').textContent = stats.total_sessions || 0;
          document.getElementById('footer-files').textContent = stats.completed_sessions || 0;

          console.log('Statistics loaded successfully');
        } else {
          throw new Error('Failed to fetch statistics');
        }
      } catch (error) {
        console.log('Using fallback statistics:', error.message);
        // Fallback to default values already in HTML
        const fallbackStats = {
          totalSessions: 12,
          totalUsers: 2,
          successRate: 80,
          processedFiles: 11
        };

        document.getElementById('total-sessions').textContent = fallbackStats.totalSessions;
        document.getElementById('total-users').textContent = fallbackStats.totalUsers;
        document.getElementById('success-rate').textContent = fallbackStats.successRate + '%';
        document.getElementById('processed-files').textContent = fallbackStats.processedFiles;

        document.getElementById('footer-sessions').textContent = fallbackStats.totalSessions;
        document.getElementById('footer-files').textContent = fallbackStats.processedFiles;
      }
    }

    // Load statistics when page loads
    document.addEventListener('DOMContentLoaded', loadStatistics);

    // Refresh statistics every 30 seconds
    setInterval(loadStatistics, 30000);

    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });

    // Add loading animation to stats
    function animateNumbers() {
      const numbers = document.querySelectorAll('.stat-number, .footer-stat-number');
      numbers.forEach(number => {
        const finalValue = parseInt(number.textContent) || 0;
        if (finalValue > 0) {
          let currentValue = 0;
          const increment = finalValue / 20;
          const timer = setInterval(() => {
            currentValue += increment;
            if (currentValue >= finalValue) {
              number.textContent = finalValue + (number.textContent.includes('%') ? '%' : '');
              clearInterval(timer);
            } else {
              number.textContent = Math.floor(currentValue) + (number.textContent.includes('%') ? '%' : '');
            }
          }, 50);
        }
      });
    }

    // Trigger number animation when stats section comes into view
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          animateNumbers();
          observer.unobserve(entry.target);
        }
      });
    });

    const statsSection = document.querySelector('.stats-summary');
    if (statsSection) {
      observer.observe(statsSection);
    }
  </script>
</body>
</html>
