# AutoSpace Troubleshooting Guide

## Quick Fix for Authentication Error

If you're seeing `{"error": "Authentication required"}`, here's how to fix it:

### Step 1: Start the Server Properly
```bash
# Use the startup script for better debugging
python start_autospace.py

# OR start manually
python backend.py
```

### Step 2: Access the Login Page
Open your browser and go to:
```
http://localhost:5000/login
```

### Step 3: Login with Default Credentials
- **Username**: `admin`
- **Password**: `admin`

### Step 4: You Should Be Redirected
After successful login, you'll be redirected to the main application.

---

## Common Issues and Solutions

### 1. "Authentication required" Error

**Problem**: Accessing `http://localhost:5000/` directly without logging in first.

**Solution**: 
- Go to `http://localhost:5000/login` first
- Login with admin/admin
- You'll be automatically redirected to the main app

### 2. Server Not Starting

**Problem**: `python backend.py` fails to start.

**Solutions**:
```bash
# Check if Flask is installed
pip install flask

# Check if you're in the right directory
ls -la  # Should see backend.py

# Use the startup script for better error messages
python start_autospace.py check
```

### 3. Login Page Not Loading

**Problem**: Browser shows connection error.

**Solutions**:
- Ensure server is running: `python backend.py`
- Check the correct URL: `http://localhost:5000/login`
- Try a different browser
- Check if port 5000 is available

### 4. Login Fails with Correct Credentials

**Problem**: admin/admin doesn't work.

**Solutions**:
```bash
# Delete user file and restart server
rm users.txt
python backend.py
```

### 5. Tools Not Working

**Problem**: File upload or tool execution fails.

**Solutions**:
```bash
# Check if python_tools directory exists
ls python_tools/

# Check file permissions
chmod +x python_tools/*.py

# Test with startup script
python start_autospace.py check
```

### 6. Session Not Updating

**Problem**: Progress doesn't update in real-time.

**Solutions**:
- Refresh the browser page
- Check browser console for JavaScript errors
- Ensure server is still running

---

## Testing Your Installation

### Quick System Check
```bash
python start_autospace.py check
```

### Test Server Connection
```bash
python start_autospace.py test
```

### Full System Test
```bash
python test_autospace.py
```

---

## Debug Mode

To enable detailed error messages, edit `backend.py`:

```python
# Change this line at the bottom:
app.run(host='0.0.0.0', port=5000, debug=True)
```

---

## Manual Verification Steps

### 1. Check File Structure
Your directory should look like this:
```
AutoSpace/
├── backend.py
├── start_autospace.py
├── test_autospace.py
├── templates/
│   ├── index.html
│   └── login.html
├── static/
│   ├── styles.css
│   └── data.js
└── python_tools/
    ├── extractor.py
    ├── meta_data.py
    └── [other tools]
```

### 2. Test Health Endpoint
```bash
curl http://localhost:5000/health
```
Should return:
```json
{
  "service": "AutoSpace",
  "status": "healthy",
  "timestamp": "...",
  "version": "1.0.0"
}
```

### 3. Test Login Endpoint
```bash
curl -X POST http://localhost:5000/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin"}'
```

---

## Browser Console Errors

### Common JavaScript Errors

1. **Module import errors**: Check that `data.js` exists in `static/` directory
2. **Fetch errors**: Server might not be running
3. **CORS errors**: Use `http://localhost:5000`, not `file://`

### How to Check Browser Console
1. Open browser Developer Tools (F12)
2. Go to Console tab
3. Look for red error messages
4. Refresh the page to see startup errors

---

## Port Issues

### If Port 5000 is Busy
Edit `backend.py` and change the port:
```python
app.run(host='0.0.0.0', port=5001, debug=True)
```

Then access: `http://localhost:5001/login`

### Check What's Using Port 5000
```bash
# Windows
netstat -ano | findstr :5000

# Linux/Mac
lsof -i :5000
```

---

## Reset Everything

If nothing works, try a complete reset:

```bash
# Stop the server (Ctrl+C)

# Delete session and user files
rm sessions.txt users.txt

# Clear browser cache and cookies for localhost

# Restart server
python start_autospace.py

# Go to http://localhost:5000/login
```

---

## Getting Help

1. **Run the startup script**: `python start_autospace.py check`
2. **Check the logs**: Look at the terminal where you started the server
3. **Test with curl**: Use the curl commands above to test endpoints
4. **Check browser console**: Look for JavaScript errors
5. **Try incognito mode**: Rules out browser cache issues

## Success Indicators

You know everything is working when:
- ✅ `http://localhost:5000/health` returns JSON
- ✅ `http://localhost:5000/login` shows the login page
- ✅ Login with admin/admin redirects to main app
- ✅ You can see tool categories in the sidebar
- ✅ File upload and tool selection works
