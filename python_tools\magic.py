#!/usr/bin/env python3
"""
Magic Tool (T007)
Complex workflow tool with vendor-specific processing:
1. Extract lines containing keywords from "keywords.xlsx"
2. Compare extracted lines to highlight changes
3. Map keywords to features using "mapping.xlsx"
4. Generate vendor-specific output files

Requirements:
- Input file ("input.xlsx") with vendor-grouped entries
- Vendor-specific keyword files
- Vendor-specific mapping databases
"""

import sys
import json
import os
from datetime import datetime
import re

class MagicProcessor:
    def __init__(self):
        self.keywords = {}
        self.mappings = {}
        self.vendors = set()
        
    def load_keywords(self, keywords_file):
        """Load keywords from file (simulated Excel reading)"""
        try:
            # In a real implementation, you'd use pandas or openpyxl
            # For now, simulate with a simple format
            if os.path.exists(keywords_file):
                print(f"Loading keywords from {keywords_file}")
                # Simulate keyword loading
                self.keywords = {
                    'vendor1': ['keyword1', 'keyword2', 'feature'],
                    'vendor2': ['spec', 'parameter', 'value'],
                    'default': ['important', 'critical', 'note']
                }
            else:
                print(f"Keywords file not found: {keywords_file}")
                self.keywords = {'default': ['important', 'critical', 'note']}
        except Exception as e:
            print(f"Error loading keywords: {e}")
            self.keywords = {'default': ['important', 'critical', 'note']}
    
    def load_mappings(self, mappings_file):
        """Load keyword-to-feature mappings (simulated Excel reading)"""
        try:
            if os.path.exists(mappings_file):
                print(f"Loading mappings from {mappings_file}")
                # Simulate mapping loading
                self.mappings = {
                    'vendor1': {
                        'keyword1': 'Feature A',
                        'keyword2': 'Feature B',
                        'feature': 'Feature C'
                    },
                    'vendor2': {
                        'spec': 'Specification',
                        'parameter': 'Parameter',
                        'value': 'Value'
                    },
                    'default': {
                        'important': 'Important Information',
                        'critical': 'Critical Information',
                        'note': 'Note'
                    }
                }
            else:
                print(f"Mappings file not found: {mappings_file}")
                self.mappings = {'default': {'important': 'Important Information'}}
        except Exception as e:
            print(f"Error loading mappings: {e}")
            self.mappings = {'default': {'important': 'Important Information'}}
    
    def extract_vendor_from_entry(self, entry):
        """Extract vendor code from entry"""
        # Look for vendor patterns in the entry
        vendor_patterns = [
            r'VENDOR[:\s]+([A-Z0-9]+)',
            r'SUPPLIER[:\s]+([A-Z0-9]+)',
            r'MFG[:\s]+([A-Z0-9]+)',
            r'^([A-Z]{2,4})\s*[-:]'
        ]
        
        for pattern in vendor_patterns:
            match = re.search(pattern, entry.upper())
            if match:
                return match.group(1).lower()
        
        return 'default'
    
    def extract_keyword_lines(self, content, vendor):
        """Extract lines containing keywords for specific vendor"""
        keywords = self.keywords.get(vendor, self.keywords.get('default', []))
        extracted_lines = []
        
        lines = content.split('\n')
        for line_num, line in enumerate(lines, 1):
            line_lower = line.lower()
            for keyword in keywords:
                if keyword.lower() in line_lower:
                    extracted_lines.append({
                        'line_number': line_num,
                        'line_content': line.strip(),
                        'keyword': keyword,
                        'vendor': vendor
                    })
                    break  # Only count each line once
        
        return extracted_lines
    
    def map_keywords_to_features(self, extracted_lines, vendor):
        """Map keywords to their corresponding features"""
        mappings = self.mappings.get(vendor, self.mappings.get('default', {}))
        
        for line_data in extracted_lines:
            keyword = line_data['keyword']
            feature = mappings.get(keyword, f"Unmapped: {keyword}")
            line_data['mapped_feature'] = feature
        
        return extracted_lines
    
    def compare_changes(self, current_lines, previous_lines=None):
        """Compare extracted lines to highlight changes"""
        if not previous_lines:
            # No previous data to compare
            for line_data in current_lines:
                line_data['change_status'] = 'NEW'
            return current_lines
        
        # Simple comparison logic
        previous_content = {line['line_content']: line for line in previous_lines}
        
        for line_data in current_lines:
            content = line_data['line_content']
            if content in previous_content:
                line_data['change_status'] = 'UNCHANGED'
            else:
                line_data['change_status'] = 'MODIFIED'
        
        return current_lines
    
    def process_entry(self, entry_content, entry_id):
        """Process a single entry through the magic workflow"""
        vendor = self.extract_vendor_from_entry(entry_content)
        self.vendors.add(vendor)
        
        # Step 1: Extract keyword lines
        extracted_lines = self.extract_keyword_lines(entry_content, vendor)
        
        # Step 2: Map keywords to features
        mapped_lines = self.map_keywords_to_features(extracted_lines, vendor)
        
        # Step 3: Compare changes (simplified - no previous data in this implementation)
        final_lines = self.compare_changes(mapped_lines)
        
        return {
            'entry_id': entry_id,
            'vendor': vendor,
            'extracted_lines': final_lines,
            'summary': {
                'total_lines': len(final_lines),
                'keywords_found': len(set(line['keyword'] for line in final_lines)),
                'features_mapped': len(set(line['mapped_feature'] for line in final_lines))
            }
        }

def process_magic_workflow(input_data, output_file, mode):
    """Process the magic workflow"""
    processor = MagicProcessor()
    
    # Load configuration files
    processor.load_keywords('keywords.xlsx')
    processor.load_mappings('mapping.xlsx')
    
    all_results = []
    vendor_results = {}
    
    total_entries = len(input_data)
    print(f"Starting Magic workflow for {total_entries} entries in {mode} mode...")
    
    for i, (line_num, content) in enumerate(input_data.items(), 1):
        content = content.strip()
        
        if not content:
            continue
        
        print(f"Processing entry {i}/{total_entries}")
        
        # Process entry
        result = processor.process_entry(content, line_num)
        result['processing_order'] = i
        result['processing_timestamp'] = datetime.now().isoformat()
        
        all_results.append(result)
        
        # Group by vendor
        vendor = result['vendor']
        if vendor not in vendor_results:
            vendor_results[vendor] = []
        vendor_results[vendor].append(result)
        
        # Write intermediate results
        if i % 5 == 0 or i == total_entries:
            write_results(all_results, vendor_results, output_file)
            if mode == 'fast' and i >= 15:  # Limit in fast mode
                print(f"Fast mode: Stopping after {i} entries")
                break
    
    # Generate vendor-specific files
    generate_vendor_files(vendor_results, output_file)
    
    print(f"Magic workflow completed. Results written to {output_file}")
    return all_results

def write_results(all_results, vendor_results, output_file):
    """Write magic workflow results to output file"""
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("AUTOSPACE MAGIC WORKFLOW RESULTS\n")
            f.write("=" * 80 + "\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Total entries processed: {len(all_results)}\n")
            f.write(f"Vendors identified: {len(vendor_results)}\n")
            f.write("=" * 80 + "\n\n")
            
            # Overall summary
            f.write("WORKFLOW SUMMARY:\n")
            f.write("-" * 40 + "\n")
            total_lines = sum(result['summary']['total_lines'] for result in all_results)
            f.write(f"Total keyword lines extracted: {total_lines}\n")
            f.write(f"Vendors processed: {', '.join(vendor_results.keys())}\n")
            f.write("\n")
            
            # Vendor breakdown
            for vendor, results in vendor_results.items():
                f.write(f"VENDOR: {vendor.upper()}\n")
                f.write("-" * 30 + "\n")
                f.write(f"Entries: {len(results)}\n")
                vendor_lines = sum(result['summary']['total_lines'] for result in results)
                f.write(f"Keyword lines: {vendor_lines}\n")
                f.write("\n")
            
            f.write("=" * 80 + "\n\n")
            
            # Detailed results
            f.write("DETAILED RESULTS:\n")
            f.write("=" * 80 + "\n")
            
            for result in all_results:
                f.write(f"Entry ID: {result['entry_id']} | Vendor: {result['vendor']}\n")
                f.write("-" * 60 + "\n")
                
                for line_data in result['extracted_lines']:
                    f.write(f"Line {line_data['line_number']}: {line_data['keyword']} -> {line_data['mapped_feature']}\n")
                    f.write(f"  Content: {line_data['line_content'][:80]}...\n")
                    f.write(f"  Status: {line_data.get('change_status', 'NEW')}\n")
                
                f.write("\n" + "=" * 80 + "\n\n")
                
    except Exception as e:
        print(f"Error writing results: {e}")

def generate_vendor_files(vendor_results, base_output_file):
    """Generate separate files for each vendor"""
    base_name = os.path.splitext(base_output_file)[0]
    
    for vendor, results in vendor_results.items():
        vendor_file = f"{base_name}_{vendor}_output.txt"
        
        try:
            with open(vendor_file, 'w', encoding='utf-8') as f:
                f.write(f"VENDOR-SPECIFIC RESULTS: {vendor.upper()}\n")
                f.write("=" * 60 + "\n")
                f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Entries processed: {len(results)}\n")
                f.write("=" * 60 + "\n\n")
                
                for result in results:
                    f.write(f"Entry ID: {result['entry_id']}\n")
                    f.write("-" * 30 + "\n")
                    
                    for line_data in result['extracted_lines']:
                        f.write(f"{line_data['mapped_feature']}: {line_data['line_content']}\n")
                    
                    f.write("\n" + "-" * 60 + "\n\n")
            
            print(f"Vendor file created: {vendor_file}")
            
        except Exception as e:
            print(f"Error creating vendor file for {vendor}: {e}")

def main():
    """Main function"""
    if len(sys.argv) != 4:
        print("Usage: python magic.py <input_json> <output_file> <mode>")
        sys.exit(1)
    
    input_json_path = sys.argv[1]
    output_file = sys.argv[2]
    mode = sys.argv[3]
    
    try:
        # Load input data
        with open(input_json_path, 'r') as f:
            input_data = json.load(f)
        
        # Process magic workflow
        process_magic_workflow(input_data, output_file, mode)
        
        # Clear input file to signal completion
        with open(input_json_path, 'w') as f:
            json.dump({}, f)
            
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
