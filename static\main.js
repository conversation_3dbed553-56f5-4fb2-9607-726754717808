// Main application logic
import { toolsData, toolCategories } from './data.js';

// Global variables
let uploadedFile = null;
let selectedTool = null;
let sessionUpdateInterval = null;
let userUpdateInterval = null;
let adminUpdateInterval = null;
let isAdmin = false;

// DOM elements
const navTabs = document.querySelectorAll('.tab');
const toolsSection = document.getElementById('tools-section');
const fileInput = document.getElementById('file-input');
const fileInfo = document.getElementById('file-info');
const fileName = document.getElementById('file-name');
const fileSize = document.getElementById('file-size');
const runButton = document.getElementById('run');
const downloadButton = document.getElementById('download');

// Utility functions
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function formatTime(dateString) {
  const date = new Date(dateString);
  const now = new Date();
  const diffMs = now - date;
  const diffMins = Math.floor(diffMs / 60000);
  const diffHours = Math.floor(diffMins / 60);
  const diffDays = Math.floor(diffHours / 24);

  if (diffMins < 1) return 'Just now';
  if (diffMins < 60) return `${diffMins}m ago`;
  if (diffHours < 24) return `${diffHours}h ago`;
  return `${diffDays}d ago`;
}

function getStatusIcon(status) {
  const icons = {
    'pending': '<i class="fas fa-clock"></i>',
    'running': '<i class="fas fa-spinner fa-spin"></i>',
    'done': '<i class="fas fa-check-circle"></i>',
    'error': '<i class="fas fa-exclamation-circle"></i>',
    'killed': '<i class="fas fa-stop-circle"></i>'
  };
  return icons[status] || '<i class="fas fa-question-circle"></i>';
}

function getStatusColor(status) {
  const colors = {
    'pending': '#ffc107',
    'running': '#17a2b8',
    'done': '#28a745',
    'error': '#dc3545',
    'killed': '#6c757d'
  };
  return colors[status] || '#6c757d';
}

// Notification system
function showNotification(message, type = 'info') {
  const notification = document.createElement('div');
  notification.className = `notification notification-${type}`;
  notification.innerHTML = `
    <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle"></i>
    <span>${message}</span>
  `;
  
  document.body.appendChild(notification);
  
  setTimeout(() => {
    notification.classList.add('show');
  }, 100);
  
  setTimeout(() => {
    notification.classList.remove('show');
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }, 3000);
}

// Session management functions
async function loadSessions() {
  try {
    const response = await fetch('/api/sessions');
    if (response.ok) {
      const sessions = await response.json();
      displaySessions(sessions);
      return sessions;
    } else if (response.status === 401) {
      window.location.href = '/login';
      return null;
    } else {
      console.error('Failed to load sessions:', response.statusText);
      return null;
    }
  } catch (error) {
    console.error('Failed to load sessions:', error);
    return null;
  }
}

function displaySessions(sessions) {
  const activeContainer = document.getElementById('active-sessions');
  const finishedContainer = document.getElementById('finished-sessions');

  if (!activeContainer || !finishedContainer) {
    console.error('Session containers not found');
    return;
  }

  const activeSessions = [];
  const finishedSessions = [];

  Object.entries(sessions).forEach(([sessionId, sessionData]) => {
    if (sessionData.status === 'done' || sessionData.status === 'error' || sessionData.status === 'killed') {
      finishedSessions.push({ id: sessionId, ...sessionData });
    } else {
      activeSessions.push({ id: sessionId, ...sessionData });
    }
  });

  // Sort sessions by creation time (newest first)
  activeSessions.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
  finishedSessions.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

  // Display only last 10 finished sessions
  const limitedFinishedSessions = finishedSessions.slice(0, 10);

  // Update session containers
  activeContainer.innerHTML = activeSessions.length > 0 ?
    activeSessions.map(session => createSessionCard(session, true)).join('') :
    '<div class="no-sessions"><i class="fas fa-info-circle"></i><p>No active sessions</p></div>';

  finishedContainer.innerHTML = limitedFinishedSessions.length > 0 ?
    limitedFinishedSessions.map(session => createSessionCard(session, false)).join('') :
    '<div class="no-sessions"><i class="fas fa-info-circle"></i><p>No completed sessions</p></div>';

  // Update counts with null checks
  const activeCountEl = document.getElementById('active-count');
  const finishedCountEl = document.getElementById('finished-count');

  if (activeCountEl) activeCountEl.textContent = activeSessions.length;
  if (finishedCountEl) finishedCountEl.textContent = limitedFinishedSessions.length;

  // Update run button state
  updateRunButtonState(activeSessions.length);
}

function createSessionCard(session, isActive) {
  const timeStr = formatTime(session.created_at);
  const statusIcon = getStatusIcon(session.status);
  const statusColor = getStatusColor(session.status);

  const progressBar = isActive && session.progress !== undefined ?
    `<div class="progress-bar">
      <div class="progress-fill" data-width="${session.progress}"></div>
      <span class="progress-text">${session.progress}%</span>
    </div>` : '';

  let actionButtons = '';
  
  if (session.status === 'done' && session.output_file) {
    // Use user copy path for copying, but filename for downloading
    const filename = session.output_file.split('/').pop().split('\\').pop();
    const userCopyPath = session.user_copy_path || session.output_file;

    actionButtons = `
      <div class="session-actions">
        <button class="download-btn" onclick="downloadFile('${filename}'); event.stopPropagation();">
          <i class="fas fa-download"></i> Download
        </button>
        <button class="copy-path-btn" onclick="copyFilePath('${userCopyPath}'); event.stopPropagation();">
          <i class="fas fa-copy"></i> Copy Path
        </button>
      </div>`;
  } else if (session.status === 'running' || session.status === 'pending') {
    actionButtons = `
      <div class="session-actions">
        <button class="kill-btn" onclick="killSession('${session.id}'); event.stopPropagation();">
          <i class="fas fa-stop"></i> Kill Session
        </button>
      </div>`;
  } else if (session.status === 'error' || session.status === 'killed') {
    actionButtons = `
      <div class="session-actions">
        <button class="retry-btn" onclick="retrySession('${session.id}'); event.stopPropagation();">
          <i class="fas fa-redo"></i> Retry
        </button>
      </div>`;
  }

  return `
    <div class="session-card ${session.status}" data-session-id="${session.id}">
      <div class="session-header">
        <span class="tool-name">${session.tool_name}</span>
        <span class="session-time">${timeStr}</span>
      </div>
      <div class="session-info">
        <span class="session-indicator" data-color="${statusColor}">
          ${statusIcon}
        </span>
        <span class="session-status">${session.status.toUpperCase()}</span>
        <span class="session-mode">${session.mode}</span>
      </div>
      ${progressBar}
      ${actionButtons}
    </div>`;
}

function updateRunButtonState(activeSessionCount = null) {
  if (activeSessionCount === null) {
    const activeCards = document.querySelectorAll('#active-sessions .session-card');
    activeSessionCount = activeCards.length;
  }

  const runBtn = document.getElementById('run');
  if (!runBtn) return; // Guard against missing element

  if (activeSessionCount >= 3) {
    runBtn.disabled = true;
    runBtn.innerHTML = '<i class="fas fa-ban"></i> Session Limit Reached';
  } else {
    runBtn.disabled = false;
    runBtn.innerHTML = '<i class="fas fa-play"></i> Run Tool';
  }
}

// Export functions for global access
window.downloadFile = function(filename) {
  window.open(`/download/${filename}`, '_blank');
};

window.copyFilePath = function(filepath) {
  navigator.clipboard.writeText(filepath).then(() => {
    showNotification('File path copied to clipboard', 'success');
  }).catch(() => {
    showNotification('Failed to copy file path', 'error');
  });
};

window.killSession = async function(sessionId) {
  if (!confirm('Are you sure you want to kill this session? This will permanently remove it from all places.')) {
    return;
  }

  try {
    const allSessionCards = document.querySelectorAll(`[data-session-id="${sessionId}"]`);
    const sessionElements = [];
    
    allSessionCards.forEach(card => {
      sessionElements.push(card);
      card.style.opacity = '0.5';
      card.style.pointerEvents = 'none';
      card.style.filter = 'grayscale(100%)';
    });

    const response = await fetch(`/api/sessions/${sessionId}/kill`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    });

    if (response.ok) {
      showNotification('Session killed and removed from all places', 'success');
      
      sessionElements.forEach((element, index) => {
        setTimeout(() => {
          element.style.transform = 'translateX(-100%) scale(0.8)';
          element.style.transition = 'all 0.4s ease-out';
          element.style.opacity = '0';
          
          setTimeout(() => {
            if (element.parentNode) {
              element.remove();
            }
            if (index === sessionElements.length - 1) {
              updateSessionCounts();
              updateRunButtonState();
            }
          }, 400);
        }, index * 100);
      });
      
      setTimeout(async () => {
        await loadSessions();
        checkAndStartPolling();
        
        const remainingCards = document.querySelectorAll(`[data-session-id="${sessionId}"]`);
        remainingCards.forEach(card => {
          if (card.parentNode) {
            card.remove();
          }
        });
      }, 600);
      
    } else {
      sessionElements.forEach(element => {
        element.style.opacity = '1';
        element.style.pointerEvents = 'auto';
        element.style.filter = 'none';
      });
      const error = await response.json();
      showNotification(`Failed to kill session: ${error.error}`, 'error');
    }
  } catch (error) {
    console.error('Error killing session:', error);
    showNotification('Network error while killing session', 'error');
    
    const allSessionCards = document.querySelectorAll(`[data-session-id="${sessionId}"]`);
    allSessionCards.forEach(card => {
      card.style.opacity = '1';
      card.style.pointerEvents = 'auto';
      card.style.filter = 'none';
    });
  }
};

window.retrySession = function(sessionId) {
  // TODO: Implement retry functionality using sessionId
  console.log('Retry requested for session:', sessionId);
  showNotification('Retry functionality coming soon. Please create a new session.', 'info');
};

function updateSessionCounts() {
  const activeCards = document.querySelectorAll('#active-sessions .session-card');
  const finishedCards = document.querySelectorAll('#finished-sessions .session-card');

  const activeCountEl = document.getElementById('active-count');
  const finishedCountEl = document.getElementById('finished-count');

  if (activeCountEl) activeCountEl.textContent = activeCards.length;
  if (finishedCountEl) finishedCountEl.textContent = finishedCards.length;

  updateRunButtonState(activeCards.length);
}

// Initialize application
document.addEventListener('DOMContentLoaded', function() {
  // Apply dynamic styles
  applyDynamicStyles();

  // Initialize the app properly
  initializeApp();

  // Set up event listeners
  setupEventListeners();
});

function applyDynamicStyles() {
  // Apply progress bar widths
  document.querySelectorAll('.progress-fill').forEach(fill => {
    const width = fill.getAttribute('data-width');
    if (width) {
      fill.style.width = width + '%';
    }
  });
  
  // Apply status colors
  document.querySelectorAll('.session-indicator').forEach(indicator => {
    const color = indicator.getAttribute('data-color');
    if (color) {
      indicator.style.color = color;
    }
  });
}

function setupEventListeners() {
  // Tab navigation
  navTabs.forEach((tab) => {
    tab.addEventListener('click', () => {
      navTabs.forEach((t) => t.classList.remove('active'));
      tab.classList.add('active');

      const categoryId = tab.getAttribute('data-category');

      if (categoryId === 'ADMIN') {
        // Show admin section
        document.getElementById('tools-section').style.display = 'none';
        document.getElementById('admin-section').style.display = 'block';

        // Load admin data and start polling
        if (isAdmin) {
          loadAdminData();
          startAdminPolling();
        }
      } else {
        // Show tools section
        document.getElementById('tools-section').style.display = 'block';
        document.getElementById('admin-section').style.display = 'none';

        // Stop admin polling
        stopAdminPolling();

        // Display tools for category
        displayToolsForCategory(categoryId);
        resetToolSelection();
      }
    });
  });

  // File input handling
  fileInput.addEventListener('change', handleFileUpload);
  
  // Button event listeners
  runButton.addEventListener('click', handleRunTool);
  downloadButton.addEventListener('click', handleDownloadSample);
  
  // Logout button
  document.getElementById('logout-btn').addEventListener('click', handleLogout);
}

// Tool management functions
function displayToolsForCategory(categoryId) {
  const category = toolCategories.find(cat => cat.id === categoryId);
  if (!category) return;

  const tools = toolsData.filter(tool => tool.category === categoryId);

  toolsSection.innerHTML = tools.map(tool => `
    <div class="tool-card" data-tool="${tool.id}">
      <div class="tool-icon">
        <i class="${tool.icon}"></i>
      </div>
      <div class="tool-info">
        <h3>${tool.name}</h3>
        <p>${tool.description}</p>
      </div>
    </div>
  `).join('');

  // Add click handlers to tool cards
  document.querySelectorAll('.tool-card').forEach(card => {
    card.addEventListener('click', () => {
      document.querySelectorAll('.tool-card').forEach(c => c.classList.remove('selected'));
      card.classList.add('selected');

      const toolId = card.getAttribute('data-tool');
      const tool = toolsData.find(t => t.id === toolId);
      if (tool) {
        selectedTool = tool;
        showToolDescription(tool);
      }
    });
  });
}

function showToolDescription(tool) {
  const descriptionContent = document.querySelector('.description-box .tool-description');
  const toolTitle = document.querySelector('.tool-name');
  const toolId = document.querySelector('.tool-id');

  toolTitle.textContent = tool.name;
  toolId.textContent = tool.id;
  descriptionContent.textContent = tool.description;

  // Show buttons
  document.querySelectorAll('.d-buttons').forEach(btn => {
    btn.classList.remove('hidden');
  });

  // Show mode selection if tool has modes
  const modeSelection = document.querySelector('.mode-selection');
  if (tool.modes && tool.modes.length > 1) {
    modeSelection.classList.remove('hidden');
    const checkbox = document.getElementById('run-mode');
    checkbox.checked = tool.defaultMode === 'comprehensive';
  } else {
    modeSelection.classList.add('hidden');
  }
}

function resetToolSelection() {
  selectedTool = null;
  document.querySelectorAll('.d-buttons').forEach(btn => {
    btn.classList.add('hidden');
  });
  document.querySelector('.mode-selection').classList.add('hidden');

  // Reset tool description
  document.querySelector('.tool-name').textContent = 'Select a tool to get started';
  document.querySelector('.tool-id').textContent = '';
  document.querySelector('.tool-description').textContent = 'Choose a tool from the categories on the left to see its description and configuration options.';
}

// File handling
function handleFileUpload(event) {
  const file = event.target.files[0];
  if (file) {
    uploadedFile = file;
    fileName.textContent = file.name;
    fileSize.textContent = `(${formatFileSize(file.size)})`;
    fileInfo.classList.remove('hidden');
  } else {
    fileInfo.classList.add('hidden');
  }
}

// Button handlers
function handleRunTool() {
  if (!selectedTool || !uploadedFile) {
    showNotification('Please select a tool and upload a file', 'error');
    return;
  }

  const runModeCheckbox = document.getElementById('run-mode');
  const mode = runModeCheckbox && runModeCheckbox.checked ? 'comprehensive' : 'fast';

  const formData = new FormData();
  formData.append('file', uploadedFile);
  formData.append('tool', selectedTool.id);
  formData.append('mode', mode);

  uploadAndRunTool(formData);
}

async function uploadAndRunTool(formData) {
  try {
    runButton.disabled = true;
    runButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Uploading...';

    const response = await fetch('/upload', {
      method: 'POST',
      body: formData
    });

    if (response.ok) {
      const data = await response.json();
      showNotification('File uploaded successfully! Processing started.', 'success');

      // Reset form
      fileInput.value = '';
      uploadedFile = null;
      fileInfo.classList.add('hidden');

      // Start running the tool
      await runTool(data.session_id);
    } else {
      const error = await response.json();
      showNotification(`Upload failed: ${error.error}`, 'error');
    }
  } catch (error) {
    console.error('Upload error:', error);
    showNotification('Network error during upload', 'error');
  } finally {
    updateRunButtonState(); // Use the proper function to update button state
  }
}

async function runTool(sessionId) {
  try {
    const response = await fetch('/runtool', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ session_id: sessionId })
    });

    if (response.ok) {
      showNotification('Tool execution started!', 'success');

      // Refresh sessions and start polling
      await loadSessions();
      checkAndStartPolling();
    } else {
      const error = await response.json();
      showNotification(`Failed to start tool: ${error.error}`, 'error');
    }
  } catch (error) {
    console.error('Run tool error:', error);
    showNotification('Network error while starting tool', 'error');
  }
}

function handleDownloadSample() {
  if (!selectedTool) {
    showNotification('Please select a tool first', 'error');
    return;
  }

  if (selectedTool.sampleFile) {
    window.open(`/download-sample/${selectedTool.sampleFile}`, '_blank');
  } else {
    showNotification('No sample file available for this tool', 'info');
  }
}

function handleLogout() {
  // Stop all polling intervals before logout
  stopSessionPolling();
  stopUserPolling();

  fetch('/logout').then(() => {
    window.location.href = '/login';
  }).catch(() => {
    window.location.href = '/login';
  });
}

// Session polling
function startSessionPolling() {
  if (sessionUpdateInterval) {
    clearInterval(sessionUpdateInterval);
  }

  sessionUpdateInterval = setInterval(async () => {
    try {
      const sessions = await loadSessions();
      if (sessions) {
        const activeSessions = Object.values(sessions).filter(s =>
          s.status === 'running' || s.status === 'pending'
        );

        if (activeSessions.length === 0) {
          stopSessionPolling();
        }
      }
    } catch (error) {
      console.error('Error during session polling:', error);
      // Continue polling even if one request fails
    }
  }, 5000);
}

function stopSessionPolling() {
  if (sessionUpdateInterval) {
    clearInterval(sessionUpdateInterval);
    sessionUpdateInterval = null;
  }
}

function checkAndStartPolling() {
  const activeSessions = document.querySelectorAll('#active-sessions .session-card');
  if (activeSessions.length > 0) {
    startSessionPolling();
  } else {
    stopSessionPolling();
  }
}

// User polling
function startUserPolling() {
  if (userUpdateInterval) {
    clearInterval(userUpdateInterval);
  }

  userUpdateInterval = setInterval(async () => {
    try {
      await loadActiveUsers();
    } catch (error) {
      console.error('Error during user polling:', error);
      // Continue polling even if one request fails
    }
  }, 30000);
}

function stopUserPolling() {
  if (userUpdateInterval) {
    clearInterval(userUpdateInterval);
    userUpdateInterval = null;
  }
}

// Active users management
async function loadActiveUsers() {
  try {
    const response = await fetch('/api/active-users');
    if (response.ok) {
      const users = await response.json();
      displayActiveUsers(users);
      return users;
    } else if (response.status === 401) {
      window.location.href = '/login';
      return null;
    } else {
      console.error('Failed to load active users:', response.statusText);
      return null;
    }
  } catch (error) {
    console.error('Failed to load active users:', error);
    return null;
  }
}

function displayActiveUsers(users) {
  const activeUsersContainer = document.getElementById('active-users-list');

  if (!activeUsersContainer) {
    console.error('Active users container not found');
    return;
  }

  if (users.length === 0) {
    activeUsersContainer.innerHTML = `
      <div class="no-users">
        <i class="fas fa-info-circle"></i>
        <p>No active users</p>
      </div>`;
  } else {
    activeUsersContainer.innerHTML = users.map(user => createUserCard(user)).join('');
  }

  // Update count with null check
  const activeUsersCountEl = document.getElementById('active-users-count');
  if (activeUsersCountEl) {
    activeUsersCountEl.textContent = users.length;
  }
}

function createUserCard(user) {
  const timeAgo = user.minutes_ago === 0 ? 'Just now' :
                 user.minutes_ago === 1 ? '1 minute ago' :
                 `${user.minutes_ago} minutes ago`;

  const statusIcon = user.is_current_user ?
    '<i class="fas fa-user-circle" style="color: #28a745;"></i>' :
    '<i class="fas fa-user"></i>';

  const sessionBadge = user.session_count > 0 ?
    `<span class="session-badge">${user.session_count} sessions</span>` : '';

  return `
    <div class="user-card ${user.is_current_user ? 'current-user' : ''}">
      <div class="user-header">
        ${statusIcon}
        <span class="username">${user.username}${user.is_current_user ? ' (You)' : ''}</span>
        ${sessionBadge}
      </div>
      <div class="user-info">
        <span class="last-activity">${timeAgo}</span>
        <span class="activity-type">${user.activity_type}</span>
      </div>
    </div>`;
}

// Performance monitoring
function showPerformance() {
  const performanceContainer = document.querySelector('.performance-metrics');
  if (!performanceContainer) return;

  // Update memory info
  const memoryInfo = document.getElementById('memory-info');
  if (memoryInfo) {
    memoryInfo.textContent = navigator.deviceMemory ? `${navigator.deviceMemory} GB` : 'Unknown';
  }

  // Update CPU info
  const cpuInfo = document.getElementById('cpu-info');
  if (cpuInfo) {
    cpuInfo.textContent = navigator.hardwareConcurrency || 'Unknown';
  }

  // Update platform info
  const platformInfo = document.getElementById('platform-info');
  if (platformInfo) {
    // Use modern User-Agent Client Hints API when available, fallback to deprecated navigator.platform
    if (navigator.userAgentData && navigator.userAgentData.platform) {
      platformInfo.textContent = navigator.userAgentData.platform;
    } else {
      // Fallback for older browsers - suppress deprecation warning
      try {
        // @ts-ignore - Using deprecated API as fallback
        platformInfo.textContent = navigator.platform || 'Unknown';
      } catch (e) {
        platformInfo.textContent = 'Unknown';
      }
    }
  }

  // Update uptime
  const startTime = Date.now();
  const uptimeInfo = document.getElementById('uptime-info');
  if (uptimeInfo) {
    setInterval(() => {
      const uptime = Math.floor((Date.now() - startTime) / 1000);
      const minutes = Math.floor(uptime / 60);
      const seconds = uptime % 60;
      uptimeInfo.textContent = `${minutes}m ${seconds}s`;
    }, 1000);
  }
}

// Initialize application
async function initializeApp() {
  try {
    // Check authentication status and get user info
    const response = await fetch('/api/sessions');
    if (response.status === 401) {
      window.location.href = '/login';
      return;
    }

    // Get current user info
    try {
      const userResponse = await fetch('/api/user');
      if (userResponse.ok) {
        const userData = await userResponse.json();
        const userInfo = document.getElementById('user-info');
        if (userInfo) {
          userInfo.textContent = `Welcome, ${userData.full_name || userData.username}`;
        }

        // Check if user is admin and show admin tab
        isAdmin = userData.email === '<EMAIL>';
        if (isAdmin) {
          const adminTab = document.getElementById('admin');
          if (adminTab) {
            adminTab.style.display = 'flex';
          }
        }
      }
    } catch (e) {
      console.error('Failed to load user info:', e);
    }

    // Load initial data
    await loadSessions();
    await loadActiveUsers();
    showPerformance();
    startSessionPolling();
    startUserPolling();

    // Set default category
    const firstTab = document.querySelector('.tab');
    if (firstTab) {
      firstTab.classList.add('active');
      const categoryId = firstTab.getAttribute('data-category');
      displayToolsForCategory(categoryId);
    }

  } catch (error) {
    console.error('Failed to initialize app:', error);
    window.location.href = '/login';
  }
}

// Admin functionality
async function loadAdminData() {
  if (!isAdmin) return;

  try {
    // Load system stats
    const statsResponse = await fetch('/api/admin/stats');
    if (statsResponse.ok) {
      const stats = await statsResponse.json();
      updateAdminStats(stats);
    }

    // Load all users
    const usersResponse = await fetch('/api/admin/users');
    if (usersResponse.ok) {
      const users = await usersResponse.json();
      updateAdminUsers(users);
    }

    // Load all sessions
    const sessionsResponse = await fetch('/api/admin/sessions');
    if (sessionsResponse.ok) {
      const sessions = await sessionsResponse.json();
      updateAdminSessions(sessions);
    }
  } catch (error) {
    console.error('Failed to load admin data:', error);
  }
}

function updateAdminStats(stats) {
  const elements = {
    'total-users-count': stats.total_users,
    'total-sessions-count': stats.total_sessions,
    'success-rate': `${stats.success_rate}%`,
    'recent-sessions': stats.recent_sessions_24h
  };

  Object.entries(elements).forEach(([id, value]) => {
    const element = document.getElementById(id);
    if (element) {
      element.textContent = value;
    }
  });
}

function updateAdminUsers(users) {
  const tableBody = document.getElementById('admin-users-table');
  if (!tableBody) return;

  if (users.length === 0) {
    tableBody.innerHTML = '<tr><td colspan="6" class="loading-row">No users found</td></tr>';
    return;
  }

  tableBody.innerHTML = users.map(user => {
    const statusClass = user.is_admin ? 'admin' : (user.is_active ? 'active' : 'inactive');
    const statusText = user.is_admin ? 'Admin' : (user.is_active ? 'Active' : 'Inactive');
    const successRate = user.total_sessions > 0 ?
      Math.round((user.completed_sessions / user.total_sessions) * 100) : 0;

    return `
      <tr>
        <td>
          <div>
            <strong>${user.full_name}</strong><br>
            <small>${user.email}</small>
          </div>
        </td>
        <td>
          <span class="user-status ${statusClass}">
            <i class="fas fa-circle"></i>
            ${statusText}
          </span>
        </td>
        <td>
          <div>
            <strong>${user.total_sessions}</strong> total<br>
            <small>${user.active_sessions} active</small>
          </div>
        </td>
        <td>${successRate}%</td>
        <td>
          ${user.last_activity ?
            new Date(user.last_activity).toLocaleString() :
            'Never'
          }
        </td>
        <td>
          <button class="admin-action-btn view" onclick="viewUserSessions('${user.email}')">
            <i class="fas fa-eye"></i> View
          </button>
        </td>
      </tr>
    `;
  }).join('');
}

function updateAdminSessions(sessions) {
  const tableBody = document.getElementById('admin-sessions-table');
  const userFilter = document.getElementById('session-user-filter');

  if (!tableBody) return;

  // Update user filter options
  if (userFilter) {
    const users = [...new Set(Object.values(sessions).map(s => s.user_id))];
    userFilter.innerHTML = '<option value="all">All Users</option>' +
      users.map(user => `<option value="${user}">${user}</option>`).join('');
  }

  const sessionArray = Object.entries(sessions).map(([id, data]) => ({id, ...data}));
  sessionArray.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

  if (sessionArray.length === 0) {
    tableBody.innerHTML = '<tr><td colspan="7" class="loading-row">No sessions found</td></tr>';
    return;
  }

  tableBody.innerHTML = sessionArray.map(session => {
    const statusClass = session.status;
    const progress = session.progress || 0;

    return `
      <tr>
        <td>
          <code>${session.id.substring(0, 8)}...</code>
        </td>
        <td>
          <div>
            <strong>${session.user_full_name || 'Unknown'}</strong><br>
            <small>${session.user_id}</small>
          </div>
        </td>
        <td>${session.tool_name}</td>
        <td>
          <span class="session-status ${statusClass}">
            ${session.status.toUpperCase()}
          </span>
        </td>
        <td>
          <div class="progress-bar">
            <div class="progress-fill" style="width: ${progress}%"></div>
            <span>${progress}%</span>
          </div>
        </td>
        <td>
          ${new Date(session.created_at).toLocaleString()}
        </td>
        <td>
          ${session.status === 'running' || session.status === 'pending' ?
            `<button class="admin-action-btn kill" onclick="killSession('${session.id}')">
              <i class="fas fa-stop"></i> Kill
            </button>` : ''
          }
          ${session.output_file ?
            `<button class="admin-action-btn view" onclick="copyFilePath('${session.user_copy_path || session.output_file}')">
              <i class="fas fa-copy"></i> Copy
            </button>` : ''
          }
        </td>
      </tr>
    `;
  }).join('');
}

function startAdminPolling() {
  if (!isAdmin) return;

  if (adminUpdateInterval) {
    clearInterval(adminUpdateInterval);
  }

  adminUpdateInterval = setInterval(async () => {
    const adminSection = document.getElementById('admin-section');
    if (adminSection && adminSection.style.display !== 'none') {
      await loadAdminData();
    }
  }, 10000); // Update every 10 seconds
}

function stopAdminPolling() {
  if (adminUpdateInterval) {
    clearInterval(adminUpdateInterval);
    adminUpdateInterval = null;
  }
}

// Admin action functions
window.viewUserSessions = function(userEmail) {
  // Filter sessions by user
  const userFilter = document.getElementById('session-user-filter');
  if (userFilter) {
    userFilter.value = userEmail;
    filterAdminSessions();
  }

  // Scroll to sessions section
  const sessionsSection = document.querySelector('.admin-sessions-section');
  if (sessionsSection) {
    sessionsSection.scrollIntoView({ behavior: 'smooth' });
  }
};

window.killSession = async function(sessionId) {
  if (!confirm('Are you sure you want to kill this session?')) {
    return;
  }

  try {
    const response = await fetch(`/api/sessions/${sessionId}/kill`, {
      method: 'POST'
    });

    if (response.ok) {
      showNotification('Session killed successfully', 'success');

      // Refresh data based on current view
      if (isAdmin && document.getElementById('admin-section').style.display !== 'none') {
        await loadAdminData(); // Refresh admin data
      } else {
        await loadSessions(); // Refresh user sessions
      }
    } else {
      const error = await response.json();
      showNotification(error.error || 'Failed to kill session', 'error');
    }
  } catch (error) {
    console.error('Error killing session:', error);
    showNotification('Network error while killing session', 'error');
  }
};

function filterAdminSessions() {
  const statusFilter = document.getElementById('session-status-filter');
  const userFilter = document.getElementById('session-user-filter');
  const tableBody = document.getElementById('admin-sessions-table');

  if (!statusFilter || !userFilter || !tableBody) return;

  const statusValue = statusFilter.value;
  const userValue = userFilter.value;

  const rows = tableBody.querySelectorAll('tr');
  rows.forEach(row => {
    const cells = row.querySelectorAll('td');
    if (cells.length < 7) return; // Skip header or empty rows

    const status = cells[3].textContent.toLowerCase().trim();
    const userEmail = cells[1].querySelector('small').textContent;

    const statusMatch = statusValue === 'all' || status === statusValue;
    const userMatch = userValue === 'all' || userEmail === userValue;

    row.style.display = (statusMatch && userMatch) ? '' : 'none';
  });
}
