{"14d4463d-147d-4d87-969f-55d6e083bdf1": {"user_id": "admin", "tool_name": "extractor", "mode": "fast", "status": "done", "progress": 100, "created_at": "2025-08-04T12:12:52.001683", "updated_at": "2025-08-04T12:16:12.470462", "output_file": "C:\\D_Partition\\Courses\\BlueAutoSpace\\AutoSpace_output_files/extractor_04-08-2025_121252_output.txt", "error_message": null, "temp_file": "temp_14d4463d-147d-4d87-969f-55d6e083bdf1.json", "tool_path": "python_tools/extractor.py"}, "430f919c-c76c-4ba0-a247-cf86dadeab90": {"user_id": "admin", "tool_name": "keyword_search", "mode": "fast", "status": "done", "progress": 100, "created_at": "2025-08-04T12:13:05.993082", "updated_at": "2025-08-04T12:16:26.304122", "output_file": "C:\\D_Partition\\Courses\\BlueAutoSpace\\AutoSpace_output_files/keyword_search_04-08-2025_121305_output.txt", "error_message": null, "temp_file": "temp_430f919c-c76c-4ba0-a247-cf86dadeab90.json", "tool_path": "python_tools/keyword_search.py"}, "fd1a326d-482d-446b-a698-01bd8d6743c7": {"user_id": "admin", "tool_name": "extractor", "mode": "fast", "status": "killed", "progress": 50, "created_at": "2025-08-04T14:22:49.903731", "updated_at": "2025-08-04T14:26:41.315947", "output_file": "AutoSpace_output_files/extractor_04-08-2025_142249_output.txt", "error_message": "Session killed by user", "temp_file": "temp_fd1a326d-482d-446b-a698-01bd8d6743c7.json", "tool_path": "python_tools/extractor.py"}, "24eb3bdb-cef0-47c0-8432-cc017f35d328": {"user_id": "<EMAIL>", "tool_name": "extractor", "mode": "normal", "status": "done", "progress": 100, "created_at": "2025-08-04T17:57:09.778506", "updated_at": "2025-08-04T17:59:19.950661", "output_file": "C:\\D_Partition\\Courses\\BlueAutoSpace\\AutoSpace_output_files/extractor_04-08-2025_175709_output.txt", "error_message": null, "temp_file": "temp_24eb3bdb-cef0-47c0-8432-cc017f35d328.json", "tool_path": "python_tools/extractor.py"}, "a0a7e1d7-06bf-4690-9d0d-e2b2472cc00b": {"user_id": "<EMAIL>", "tool_name": "extractor", "mode": "normal", "status": "done", "progress": 100, "created_at": "2025-08-05T14:06:58.040505", "updated_at": "2025-08-05T14:10:08.327827", "output_file": "C:\\D_Partition\\Courses\\BlueAutoSpace\\AutoSpace_output_files/extractor_05-08-2025_140658_output.txt", "error_message": "Killed by user", "temp_file": "temp_a0a7e1d7-06bf-4690-9d0d-e2b2472cc00b.json", "tool_path": "python_tools/extractor.py"}}