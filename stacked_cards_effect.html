<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Stacked Cards</title>
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link
    href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@200;300;400;500;600;700;800&family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap"
    rel="stylesheet"
  />
  <style>
    body{
      font-family: poppins;
    }
    .title{
      width: 420px;
      font-size: 84px;
      font-weight: bold;
      line-height: 80px;

    }
    .stack-area{
      width:100%;
      height: 500vh;
      background: white;
      display: flex;
    }
    .left, .right{
      height: 100vh;
      flex-basis: 50%;
      top: 0;
      position: sticky;
    }

    .left{
      display:flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
    }

    button{
      font-size: 14px;
      border: none;
      border-radius: 8mm;
      padding: 15px 30px;
      background: black;
      color: white;
      cursor: pointer;
      margin-top: 20px;
    }

    .sub-title{
      width: 420px;
      margin-top: 30px;
    }

    .card-element{
      width: 350px;
      height: 350px;
      border-radius: 25px;
      margin-bottom: 10px;
      position: absolute;
      top: calc(50% - 175px);
      left: calc(50% - 175px);
      transition: 0.5s ease-in-out;
    }

    .card-element{
      box-sizing: border-box;
      padding: 35px;
      display: flex;
      justify-content: space-between;
      flex-direction: column;
      &:hover{
        filter: brightness(85%);
      }
    }

    .sub{
      font-size: 20px;
      font-weight: 700;
    }

    .content{
      font-size: 44px;
      font-weight: 700;
      line-height: 54px;
    }

    .card-element:nth-child(1) {
        background: rgb(64, 122, 255);
      }
    .card-element:nth-child(2) {
      background: rgb(221, 62, 88);
    }
    .card-element:nth-child(3) {
      background: rgb(186, 113, 245);
    }
    .card-element:nth-child(4) {
      background: rgb(247, 92, 208);
    }
    .away {
        transform-origin: bottom left;
      }

  </style>
</head>
<body>
  <div class="stack-area">
    <div class= "left">
      <div class="title">  
         Dot. Space
      </div>
      <div class="sub-title">The moonlight dances on the sea,
        Whispers of the night set me free,
        Stars above, the world’s decree.
        <button>See More Details</button>
      </div>

    </div>
    <div class="right">
      <div class="card-element">
        <div class="sub">Simplified</div>
        <div class="content">Complex tasks are now simple</div>
      </div>
      <div class="card-element">
        <div class="sub">Boost Productivity</div>
        <div class="content">Perform Tasks in less time</div>
      </div>
      <div class="card-element">
        <div class="sub">Facilitated learning</div>
        <div class="content">train anyone from anywhere</div>
      </div>
      <div class="card-element">
        <div class="sub">Support</div>
        <div class="content">Now its 24/7 support</div>
      </div>
    </div>
  </div>

  </div>
  <script>
    const cards = document.querySelectorAll('.card-element')
    const stackArea = document.querySelector(".stack-area");
    function rotateCards(){
      let angle = 0;
      cards.forEach((card, index) => {
        if (card.classList.contains("away")) {
            card.style.transform = `translateY(-150vh) rotate(-120deg)`;
          } else {
            card.style.transform = ` rotate(${angle}deg)`;
            angle = angle - 10;
            card.style.zIndex = cards.length - index;
          }
      })
    }
    rotateCards()
    window.addEventListener("scroll", () => {
        let distance = window.innerHeight;

        let topVal = stackArea.getBoundingClientRect().top;

        let index = -1 * (topVal / distance + 1);

        index = Math.floor(index);

        for (i = 0; i < cards.length; i++) {
          if (i <= index) {
            cards[i].classList.add("away");
          } else {
            cards[i].classList.remove("away");
          }
        }
        rotateCards();
      });
  </script>
</body>
</html>