<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AutoSpace - Login</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;600;700&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Montserrat', sans-serif;
        }

        .login-container {
            background: white;
            padding: 3rem;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }

        .login-header {
            margin-bottom: 2rem;
        }

        .login-header h1 {
            color: #333;
            font-size: 2.5rem;
            font-weight: 700;
            letter-spacing: 2px;
            margin-bottom: 0.5rem;
        }

        .login-header p {
            color: #666;
            font-size: 1rem;
            font-weight: 300;
        }

        .login-form {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .form-group {
            position: relative;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 600;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .form-group input {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-group i {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            margin-top: 12px;
        }

        .login-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 1rem;
        }

        .login-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .login-button:active {
            transform: translateY(0);
        }

        .login-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .error-message {
            background: #fee;
            color: #c33;
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid #c33;
            margin-bottom: 1rem;
            display: none;
        }

        .success-message {
            background: #efe;
            color: #3c3;
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid #3c3;
            margin-bottom: 1rem;
            display: none;
        }

        .loading {
            display: none;
            margin-top: 1rem;
        }

        .loading i {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .default-credentials {
            margin-top: 2rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #17a2b8;
        }

        .default-credentials h4 {
            color: #17a2b8;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .default-credentials p {
            color: #666;
            font-size: 0.8rem;
            margin: 0;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>AUTOSPACE</h1>
            <p>Document Processing Platform</p>
        </div>

        <div class="error-message" id="error-message"></div>
        <div class="success-message" id="success-message"></div>

        <form class="login-form" id="login-form">
            <div class="form-group">
                <label for="email">Email Address</label>
                <input type="email" id="email" name="email" required placeholder="<EMAIL>">
                <i class="fas fa-envelope"></i>
            </div>

            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required>
                <i class="fas fa-lock"></i>
            </div>

            <button type="submit" class="login-button" id="login-button">
                <span id="button-text">Login</span>
                <div class="loading" id="loading">
                    <i class="fas fa-spinner"></i> Logging in...
                </div>
            </button>
        </form>

        <div class="signup-link" style="margin-top: 1rem; padding: 1rem; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #28a745;">
            <h4 style="color: #28a745; margin-bottom: 0.5rem; font-size: 0.9rem; text-transform: uppercase; letter-spacing: 1px;">New User?</h4>
            <p style="color: #666; font-size: 0.9rem; margin: 0;"><a href="/signup" style="color: #667eea; text-decoration: none; font-weight: 600;">Create an account</a></p>
        </div>

        <div class="about-link" style="margin-top: 1rem; padding: 1rem; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #667eea;">
            <h4 style="color: #667eea; margin-bottom: 0.5rem; font-size: 0.9rem; text-transform: uppercase; letter-spacing: 1px;">Explore</h4>
            <p style="color: #666; font-size: 0.9rem; margin: 0;">
                <a href="/home" style="color: #667eea; text-decoration: none; font-weight: 600;">Dashboard</a> |
                <a href="/about" style="color: #667eea; text-decoration: none; font-weight: 600;">About</a>
            </p>
        </div>
    </div>

    <script>
        // Email validation
        function validateEmail(email) {
            const emailPattern = /^[a-zA-Z0-9._%+-]+@siliconexpert\.com$/;
            return emailPattern.test(email);
        }

        // Real-time email validation
        document.getElementById('email').addEventListener('input', function() {
            const email = this.value;
            if (email && !validateEmail(email)) {
                this.style.borderColor = '#dc3545';
                this.setCustomValidity('Email must be from @siliconexpert.com domain');
            } else {
                this.style.borderColor = email ? '#28a745' : '';
                this.setCustomValidity('');
            }
        });

        document.getElementById('login-form').addEventListener('submit', async (e) => {
            e.preventDefault();

            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const button = document.getElementById('login-button');
            const buttonText = document.getElementById('button-text');
            const loading = document.getElementById('loading');
            const errorMessage = document.getElementById('error-message');
            const successMessage = document.getElementById('success-message');

            // Validate email before submission
            if (!validateEmail(email)) {
                errorMessage.textContent = 'Please enter a valid @siliconexpert.com email address';
                errorMessage.style.display = 'block';
                successMessage.style.display = 'none';
                return;
            }

            // Hide previous messages
            errorMessage.style.display = 'none';
            successMessage.style.display = 'none';

            // Show loading state
            button.disabled = true;
            buttonText.style.display = 'none';
            loading.style.display = 'block';

            try {
                const response = await fetch('/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password })
                });

                const data = await response.json();

                if (response.ok) {
                    successMessage.textContent = 'Login successful! Redirecting...';
                    successMessage.style.display = 'block';
                    
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 1500);
                } else {
                    errorMessage.textContent = data.error || 'Login failed';
                    errorMessage.style.display = 'block';
                }
            } catch (error) {
                errorMessage.textContent = 'Network error. Please try again.';
                errorMessage.style.display = 'block';
            } finally {
                // Reset button state
                button.disabled = false;
                buttonText.style.display = 'block';
                loading.style.display = 'none';
            }
        });

        // Auto-fill default credentials for demo
        document.getElementById('email').value = '<EMAIL>';
        document.getElementById('password').value = 'admin';

        // Check for URL parameters (e.g., account created message)
        const urlParams = new URLSearchParams(window.location.search);
        const message = urlParams.get('message');
        if (message === 'account_created') {
            successMessage.textContent = 'Account created successfully! Please log in with your credentials.';
            successMessage.style.display = 'block';
        }
    </script>
</body>
</html>
