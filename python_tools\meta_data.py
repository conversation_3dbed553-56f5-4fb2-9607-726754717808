#!/usr/bin/env python3
"""
Meta Data Extractor Tool (T003)
Extracts document metadata including title, creation date, modification date, author, and other properties.
"""

import sys
import json
import os
from datetime import datetime
import mimetypes
import hashlib

def extract_metadata(file_path):
    """Extract metadata from a file"""
    metadata = {}
    
    try:
        # Basic file information
        stat_info = os.stat(file_path)
        
        metadata['file_name'] = os.path.basename(file_path)
        metadata['file_path'] = file_path
        metadata['file_size'] = stat_info.st_size
        metadata['file_size_human'] = format_file_size(stat_info.st_size)
        
        # Timestamps
        metadata['creation_time'] = datetime.fromtimestamp(stat_info.st_ctime).isoformat()
        metadata['modification_time'] = datetime.fromtimestamp(stat_info.st_mtime).isoformat()
        metadata['access_time'] = datetime.fromtimestamp(stat_info.st_atime).isoformat()
        
        # MIME type
        mime_type, encoding = mimetypes.guess_type(file_path)
        metadata['mime_type'] = mime_type or 'unknown'
        metadata['encoding'] = encoding or 'unknown'
        
        # File extension
        metadata['file_extension'] = os.path.splitext(file_path)[1].lower()
        
        # File hash (MD5)
        metadata['md5_hash'] = calculate_file_hash(file_path)
        
        # Try to extract additional metadata based on file type
        if metadata['file_extension'] in ['.txt', '.log', '.csv']:
            metadata.update(extract_text_metadata(file_path))
        elif metadata['file_extension'] in ['.pdf']:
            metadata.update(extract_pdf_metadata(file_path))
        elif metadata['file_extension'] in ['.docx', '.doc']:
            metadata.update(extract_word_metadata(file_path))
        elif metadata['file_extension'] in ['.xlsx', '.xls']:
            metadata.update(extract_excel_metadata(file_path))
        
    except Exception as e:
        metadata['error'] = f"Failed to extract metadata: {str(e)}"
    
    return metadata

def format_file_size(size_bytes):
    """Convert bytes to human readable format"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.2f} {size_names[i]}"

def calculate_file_hash(file_path):
    """Calculate MD5 hash of file"""
    try:
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except Exception:
        return "unknown"

def extract_text_metadata(file_path):
    """Extract metadata from text files"""
    metadata = {}
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
            
        metadata['line_count'] = len(content.splitlines())
        metadata['character_count'] = len(content)
        metadata['word_count'] = len(content.split())
        metadata['encoding_detected'] = 'utf-8'
        
        # Check if file is empty
        metadata['is_empty'] = len(content.strip()) == 0
        
    except Exception as e:
        metadata['text_metadata_error'] = str(e)
    
    return metadata

def extract_pdf_metadata(file_path):
    """Extract metadata from PDF files (basic implementation)"""
    metadata = {}
    try:
        # This is a basic implementation - in production you'd use PyPDF2 or similar
        metadata['document_type'] = 'PDF Document'
        metadata['requires_pdf_library'] = 'Install PyPDF2 for detailed PDF metadata'
        
        # Try to read first few bytes to confirm PDF
        with open(file_path, 'rb') as f:
            header = f.read(8)
            if header.startswith(b'%PDF-'):
                version = header[5:8].decode('ascii', errors='ignore')
                metadata['pdf_version'] = version
            else:
                metadata['pdf_error'] = 'Not a valid PDF file'
                
    except Exception as e:
        metadata['pdf_metadata_error'] = str(e)
    
    return metadata

def extract_word_metadata(file_path):
    """Extract metadata from Word documents (basic implementation)"""
    metadata = {}
    try:
        metadata['document_type'] = 'Microsoft Word Document'
        metadata['requires_docx_library'] = 'Install python-docx for detailed Word metadata'
        
        # Basic file type detection
        if file_path.endswith('.docx'):
            metadata['word_format'] = 'Office Open XML'
        else:
            metadata['word_format'] = 'Legacy Word Format'
            
    except Exception as e:
        metadata['word_metadata_error'] = str(e)
    
    return metadata

def extract_excel_metadata(file_path):
    """Extract metadata from Excel files (basic implementation)"""
    metadata = {}
    try:
        metadata['document_type'] = 'Microsoft Excel Spreadsheet'
        metadata['requires_excel_library'] = 'Install openpyxl for detailed Excel metadata'
        
        # Basic file type detection
        if file_path.endswith('.xlsx'):
            metadata['excel_format'] = 'Office Open XML'
        else:
            metadata['excel_format'] = 'Legacy Excel Format'
            
    except Exception as e:
        metadata['excel_metadata_error'] = str(e)
    
    return metadata

def process_file_list(input_data, output_file, mode):
    """Process a list of files and extract metadata"""
    results = []
    total_files = len(input_data)
    
    print(f"Starting metadata extraction for {total_files} files in {mode} mode...")
    
    for i, (line_num, file_path) in enumerate(input_data.items(), 1):
        file_path = file_path.strip()
        
        if not file_path:
            continue
            
        print(f"Processing file {i}/{total_files}: {file_path}")
        
        # Extract metadata
        metadata = extract_metadata(file_path)
        metadata['line_number'] = line_num
        metadata['processing_order'] = i
        metadata['processing_timestamp'] = datetime.now().isoformat()
        
        results.append({
            'line_number': line_num,
            'file_path': file_path,
            'metadata': metadata
        })
        
        # Write intermediate results
        if i % 10 == 0 or i == total_files:
            write_results(results, output_file)
            if mode == 'fast' and i >= 50:  # Limit in fast mode
                print(f"Fast mode: Stopping after {i} files")
                break
    
    print(f"Metadata extraction completed. Results written to {output_file}")
    return results

def write_results(results, output_file):
    """Write results to output file"""
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("AUTOSPACE METADATA EXTRACTION RESULTS\n")
            f.write("=" * 80 + "\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Total files processed: {len(results)}\n")
            f.write("=" * 80 + "\n\n")
            
            for result in results:
                f.write(f"File #{result['line_number']}: {result['file_path']}\n")
                f.write("-" * 60 + "\n")
                
                metadata = result['metadata']
                for key, value in metadata.items():
                    if key != 'line_number':
                        f.write(f"{key.replace('_', ' ').title()}: {value}\n")
                
                f.write("\n" + "=" * 80 + "\n\n")
                
    except Exception as e:
        print(f"Error writing results: {e}")

def main():
    """Main function"""
    if len(sys.argv) != 4:
        print("Usage: python meta_data.py <input_json> <output_file> <mode>")
        sys.exit(1)
    
    input_json_path = sys.argv[1]
    output_file = sys.argv[2]
    mode = sys.argv[3]
    
    try:
        # Load input data
        with open(input_json_path, 'r') as f:
            input_data = json.load(f)
        
        # Process files
        process_file_list(input_data, output_file, mode)
        
        # Clear input file to signal completion
        with open(input_json_path, 'w') as f:
            json.dump({}, f)
            
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
