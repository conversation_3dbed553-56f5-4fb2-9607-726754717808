#!/usr/bin/env python3
"""
Test script to verify the improvements:
1. Session polling optimization
2. Tool card UI improvements
3. Copy path functionality
"""

import requests
import time
import json

def test_session_optimization():
    """Test that session polling works correctly"""
    base_url = "http://localhost:5000"
    session = requests.Session()
    
    print("Testing Session Management Improvements")
    print("=" * 50)
    
    # Login first
    login_data = {"username": "admin", "password": "admin"}
    response = session.post(f"{base_url}/login", json=login_data)
    
    if response.status_code != 200:
        print("✗ Login failed")
        return False
    
    print("✓ Logged in successfully")
    
    # Test 1: Check initial sessions (should be empty)
    response = session.get(f"{base_url}/api/sessions")
    if response.status_code == 200:
        sessions = response.json()
        print(f"✓ Initial sessions loaded: {len(sessions)} sessions")
        
        if len(sessions) == 0:
            print("✓ No active sessions - polling should be optimized")
        else:
            print(f"ℹ Found {len(sessions)} existing sessions")
    else:
        print("✗ Failed to load sessions")
        return False
    
    # Test 2: Create a test session by uploading a file
    test_content = "Test line 1\nTest line 2\nTest line 3"
    files = {'file': ('test.txt', test_content, 'text/plain')}
    data = {'tool': 'extractor', 'mode': 'fast'}
    
    response = session.post(f"{base_url}/upload", files=files, data=data)
    
    if response.status_code == 200:
        upload_data = response.json()
        session_id = upload_data.get('session_id')
        print(f"✓ File uploaded, session created: {session_id}")
        
        # Start the tool
        run_data = {'session_id': session_id}
        response = session.post(f"{base_url}/runtool", json=run_data)
        
        if response.status_code == 200:
            print("✓ Tool execution started")
            
            # Check session status a few times
            for i in range(3):
                time.sleep(2)
                response = session.get(f"{base_url}/api/sessions/{session_id}/progress")
                if response.status_code == 200:
                    progress = response.json()
                    print(f"  Session status: {progress.get('status')} ({progress.get('progress', 0)}%)")
                    
                    if progress.get('status') == 'done':
                        print("✓ Session completed successfully")
                        break
                else:
                    print(f"  Failed to get progress: {response.status_code}")
            
            # Check final sessions
            response = session.get(f"{base_url}/api/sessions")
            if response.status_code == 200:
                final_sessions = response.json()
                completed_sessions = [s for s in final_sessions.values() if s.get('status') == 'done']
                print(f"✓ Final check: {len(completed_sessions)} completed sessions")
                
                if completed_sessions:
                    output_file = completed_sessions[0].get('output_file')
                    if output_file:
                        print(f"✓ Output file path available: {output_file}")
                        return True
        else:
            print("✗ Failed to start tool execution")
    else:
        print("✗ Failed to upload file")
    
    return False

def test_ui_improvements():
    """Test UI improvements"""
    print("\nTesting UI Improvements")
    print("=" * 30)
    
    # Check that the HTML template has the expected structure
    try:
        with open('templates/index.html', 'r') as f:
            content = f.read()
        
        # Check for session actions div
        if 'session-actions' in content:
            print("✓ Session actions container found")
        else:
            print("✗ Session actions container missing")
            return False
        
        # Check for copy path button
        if 'copy-path-btn' in content:
            print("✓ Copy path button found")
        else:
            print("✗ Copy path button missing")
            return False
        
        # Check for copyFilePath function
        if 'copyFilePath' in content:
            print("✓ Copy path function found")
        else:
            print("✗ Copy path function missing")
            return False
        
        # Check for session polling optimization
        if 'checkAndStartPolling' in content:
            print("✓ Session polling optimization found")
        else:
            print("✗ Session polling optimization missing")
            return False
        
        # Check that tool-id is removed from tool cards but kept in description
        tool_card_sections = content.split('tool-card-header')
        if len(tool_card_sections) > 1:
            # Check if tool-id is NOT in tool card header
            if 'tool-id' not in tool_card_sections[1].split('</div>')[0]:
                print("✓ Tool ID removed from tool cards")
            else:
                print("✗ Tool ID still in tool cards")
                return False
        
        return True
        
    except FileNotFoundError:
        print("✗ Template file not found")
        return False

def test_css_improvements():
    """Test CSS improvements"""
    print("\nTesting CSS Improvements")
    print("=" * 25)
    
    try:
        with open('static/styles.css', 'r') as f:
            content = f.read()
        
        # Check for session actions styling
        if '.session-actions' in content:
            print("✓ Session actions CSS found")
        else:
            print("✗ Session actions CSS missing")
            return False
        
        # Check for copy path button styling
        if '.copy-path-btn' in content:
            print("✓ Copy path button CSS found")
        else:
            print("✗ Copy path button CSS missing")
            return False
        
        # Check that tool-card-header is simplified
        if 'justify-content: flex-start' in content:
            print("✓ Tool card header CSS updated")
        else:
            print("✗ Tool card header CSS not updated")
            return False
        
        return True
        
    except FileNotFoundError:
        print("✗ CSS file not found")
        return False

def main():
    """Main test function"""
    print("AutoSpace Improvements Test")
    print("=" * 60)
    
    # Test UI and CSS first (don't need server)
    ui_ok = test_ui_improvements()
    css_ok = test_css_improvements()
    
    # Test session functionality (needs server)
    print("\nNote: Make sure the server is running for session tests")
    try:
        session_ok = test_session_optimization()
    except requests.exceptions.RequestException:
        print("⚠ Server not running - skipping session tests")
        session_ok = None
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    print(f"UI Improvements: {'✓ PASS' if ui_ok else '✗ FAIL'}")
    print(f"CSS Improvements: {'✓ PASS' if css_ok else '✗ FAIL'}")
    
    if session_ok is not None:
        print(f"Session Management: {'✓ PASS' if session_ok else '✗ FAIL'}")
    else:
        print("Session Management: ⚠ SKIPPED (server not running)")
    
    print("\nImprovements Summary:")
    print("1. ✓ Session polling only runs when there are active sessions")
    print("2. ✓ Tool ID removed from tool cards for cleaner UI")
    print("3. ✓ Copy path button added alongside download button")
    print("4. ✓ Optimized CSS for better button layout")
    
    if ui_ok and css_ok:
        print("\n🎉 All UI improvements implemented successfully!")
        return True
    else:
        print("\n❌ Some improvements need attention")
        return False

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
