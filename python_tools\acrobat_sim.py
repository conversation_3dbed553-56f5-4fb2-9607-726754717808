import fitz
import re
import random
from difflib import Se<PERSON><PERSON>atcher
from multiprocessing import freeze_support
import fitz
import re
from tqdm import tqdm
from rich.console import Console
from rich.markdown import Markdown
from difflib import SequenceMatcher
import concurrent.futures
import sys
import json

class Word:
    def __init__(self, word_text, word_start, word_end, word_up, word_down, word_page):
        self.word_text = word_text
        self.word_start = word_start
        self.word_end = word_end
        self.word_up = word_up
        self.word_down = word_down
        self.word_page = word_page

    def __eq__(self, other):
        return (self.word_text == other.word_text and self.word_start == other.word_start
                and self.word_end == other.word_end and self.word_up == other.word_up and self.word_page == other.word_page)

    def __hash__(self):
        return hash((self.word_text, self.word_start, self.word_end, self.word_up))

    @property
    def word_coordinate(self):
        return self.word_up + self.word_down
    
    def word_underline(self):
        return  [(self.word_start, self.word_down), (self.word_end, self.word_down)]


        
class Block:
    def __init__(self, block_text, block_original_text, block_start, block_end, block_up, block_down, block_page, block_words):
        self.block_text = block_text
        self.block_original_text = block_original_text
        self.block_start = block_start
        self.block_end = block_end
        self.block_up = block_up
        self.block_down = block_down
        self.block_page = block_page
        self.block_words = block_words
    @property
    def coordinate_pairs(self):
        return [(self.block_start, self.block_up), (self.block_end, self.block_down)]
    @property
    def coordinate(self):
        return (self.block_start, self.block_end,self.block_up, self.block_down)
    
    def __hash__(self):
        return hash(self.block_text)
    
    def __eq__(self, other):
        return self.block_text == other.block_text


def extract_all_words(doc):
    start_index, end_index, up_index, down_index, text_index = 0, 2, 1, 3, 4
    all_words = []
    for page_index, page in enumerate(doc):
        for ext_word in page.get_text("words"):
            all_words.append(Word(ext_word[text_index]
                                  , ext_word[start_index]
                                  , ext_word[end_index]
                                  , ext_word[up_index]
                                  , ext_word[down_index]
                                  , page_index))
    return all_words

def count_of_pages(doc):
    return doc.page_count

def extract_all_blocks(doc):
    start_index, end_index, up_index, down_index, text_index = 0, 2, 1, 3, 4
    all_blocks = []
    for page_index, page in enumerate(doc):
        for ext_block in page.get_text("blocks"):
            if "<image:" in ext_block[text_index] or ext_block[text_index].strip() == '':
                continue
            all_blocks.append(Block(re.sub(r"(\s+|\n)", " ", ext_block[text_index]).strip()
                                  , ext_block[text_index]
                                  , ext_block[start_index]
                                  , ext_block[end_index]
                                  , ext_block[up_index]
                                  , ext_block[down_index]
                                  , page_index,[]))
    return all_blocks

def add_words_to_blocks(blocks, words):
    for block in blocks:
        for word in words:
            if word.word_page == block.block_page and block.block_start <= word.word_start <= block.block_end and block.block_up <= word.word_up <= block.block_down:
                block.block_words.append(word)
    
def remove_date(line):
    date_pattern = re.compile(r"(20[0-9]{2}[/._-]+[0-9]{1,2}[/._-]+[0-9]{1,2}"
                              r"|[0-9]{1,2}[/._-]+[0-9]{1,2}[/._-]+20[0-9]{2}"
                              r"|[0-9]{1,2}[/._-]+20[0-9]{2}"
                              r"|[0-9]{1,2}[/._-]+[0-9]{1,2}[/._-]+(2[0-4]|1[0-9])"
                              r"|(20[0-9]{2}|2[0-4]|1[0-9])[/._,\s-]+(jan|feb|mar|apr|may|june|jun|july|jul|aug|sept|sep|oct|nov|dec)[/._,\s-]+[0-9]{1,2}"
                              r"|[0-9]{1,2}[/._,\s-]+(jan|feb|mar|apr|may|june|jun|july|jul|aug|sept|sep|oct|nov|dec)[/._,\s-]+(20[0-9]{2}|2[0-4]|1[0-9])"
                              r"|(jan|feb|mar|apr|may|june|jun|july|jul|aug|sept|sep|oct|nov|dec)[/.,\s-]+[0-9]{1,2}[/.,\s-]+(20[0-9]{2}|2[0-4]|1[0-9])"
                              r"|(jan|feb|mar|apr|may|june|jun|july|jul|aug|sept|sep|oct|nov|dec)[/.,\s-]+(20[0-9]{2}|2[0-4]|1[0-9])"
                              r"|[0-9]{1,2}[/.\s-]+(january|february|march|april|may|june|july|august|september|october|november|december)[/.,\s-]+(20[0-9]{2}|2[0-4]|1[0-9])"
                              r"|(20[0-9]{2}|2[0-4]|1[0-9])[/._,\s-]+(january|february|march|april|may|june|july|august|september|october|november|december)[/._,\s-]+[0-9]{1,2}"
                              r"|(january|february|march|april|may|june|july|august|september|october|november|december)[/.,\s-]+[0-9]{1,2}[/.,\s-]+(20[0-9]{2}|2[0-4]|1[0-9])"
                              r"|(january|february|march|april|may|june|july|august|september|october|november|december)[/.,\s-]+(20[0-9]{2}|2[0-4]|1[0-9])"
                              r"|[0-9]{1,2}:[0-9]{1,2}:[0-9]{1,2}(\s[pa]m|)"
                              r"|[0-9]{1,2}:[0-9]{1,2}(\s[pa]m|))"
                              , flags=re.IGNORECASE)
    
    line = date_pattern.sub("", line)
    line = re.sub(r"\s+", " ", line).strip()
    return line


def compare_doc_text(cm_text, rev_text, cm_block_words, rev_block_words):
    sm = SequenceMatcher(None, cm_text.split(), rev_text.split())
    tag = 0
    i1 = 1
    i2 = 2
    j1 = 3
    j2 = 4
    added = []
    removed = []
    changed = []
    matched = []
    for x in sm.get_opcodes():

        if x[tag] == 'equal':
            matched.extend(cm_block_words[x[i1]:x[i2]])

        if x[tag] == 'insert':
            if rev_text.split()[x[j1]] == rev_block_words[x[j1]].word_text:
                added.extend(rev_block_words[x[j1]:x[j2]])
            else:
                distance = x[j2]-x[j1]
                start = x[j1] - 10 if (x[j1] - 10)>=0 else 0
                end =   x[j2] + 10 if (x[j1] - 10)<len(rev_block_words) else len(rev_block_words)
                print(start, end, distance)
                for word in rev_block_words[start:end]:
                    if word.word_text == rev_text.split()[x[j1]]:
                        new_start = rev_block_words[start:end].index(word)+start
                        new_end = new_start+distance
                        added.extend(rev_block_words[new_start:new_end])
                        break

        elif x[tag] == 'delete':
            if cm_text.split()[x[i1]] == cm_block_words[x[i1]].word_text:
                removed.extend(cm_block_words[x[i1]:x[i2]])
            else:
                distance = x[i2]-x[i1]
                start = x[i1] - 10 if (x[i1] - 10)>=0 else 0
                end =   x[i2] + 10 if (x[i1] - 10)<len(cm_block_words) else len(cm_block_words)

                for word in cm_block_words[start:end]:
                    if word.word_text == cm_text.split()[x[i1]]:
                        new_start = cm_block_words[start:end].index(word)+start
                        new_end = new_start+distance
                        removed.extend(cm_block_words[new_start:new_end])
                        break

        elif x[tag] == 'replace':

            if cm_text.split()[x[i1]] == cm_block_words[x[i1]].word_text:
                    cm_changed_text= cm_block_words[x[i1]:x[i2]] 
            else:
                distance = x[i2]-x[i1]
                start = x[i1] - 10 if (x[i1] - 10)>=0 else 0
                end =   x[i2] + 10 if (x[i1] - 10)<len(cm_block_words) else len(cm_block_words)
                for word in cm_block_words[start:end]:
                    if word.word_text == cm_text.split()[x[i1]]:
                        new_start = cm_block_words[start:end].index(word)+start
                        new_end = new_start+distance
                        cm_changed_text = cm_block_words[new_start:new_end]
                        break


            if rev_text.split()[x[j1]] == rev_block_words[x[j1]].word_text:
                rev_changed_text = rev_block_words[x[j1]:x[j2]]

            else:
                distance = x[j2]-x[j1]
                start = x[j1] - 10 if (x[j1] - 10)>=0 else 0
                end =   x[j2] + 10 if (x[j1] - 10)<len(rev_block_words) else len(rev_block_words)

                for word in rev_block_words[start:end]:
                    if word.word_text == rev_text.split()[x[j1]]:
                        new_start = rev_block_words[start:end].index(word)+start
                        new_end = new_start+distance
                        rev_changed_text = rev_block_words[new_start:new_end]
                        break

            changed.append((cm_changed_text, rev_changed_text))

    return {
    "added": added,
    "removed": removed,
    "changed": changed,
    "matched":matched}


def remove_date(text):
    date_pattern = re.compile(r"(20[0-9]{2}[/._-]+[0-9]{1,2}[/._-]+[0-9]{1,2}"
                              r"|[0-9]{1,2}[/._-]+[0-9]{1,2}[/._-]+20[0-9]{2}"
                              r"|[0-9]{1,2}[/._-]+20[0-9]{2}"
                              r"|[0-9]{1,2}[/._-]+[0-9]{1,2}[/._-]+(2[0-4]|1[0-9])"
                              r"|(20[0-9]{2}|2[0-4]|1[0-9])[/._,\s-]+(jan|feb|mar|apr|may|june|jun|july|jul|aug|sept|sep|oct|nov|dec)[/._,\s-]+[0-9]{1,2}"
                              r"|[0-9]{1,2}[/._,\s-]+(jan|feb|mar|apr|may|june|jun|july|jul|aug|sept|sep|oct|nov|dec)[/._,\s-]+(20[0-9]{2}|2[0-4]|1[0-9])"
                              r"|(jan|feb|mar|apr|may|june|jun|july|jul|aug|sept|sep|oct|nov|dec)[/.,\s-]+[0-9]{1,2}[/.,\s-]+(20[0-9]{2}|2[0-4]|1[0-9])"
                              r"|(jan|feb|mar|apr|may|june|jun|july|jul|aug|sept|sep|oct|nov|dec)[/.,\s-]+(20[0-9]{2}|2[0-4]|1[0-9])"
                              r"|[0-9]{1,2}[/.\s-]+(january|february|march|april|may|june|july|august|september|october|november|december)[/.,\s-]+(20[0-9]{2}|2[0-4]|1[0-9])"
                              r"|(20[0-9]{2}|2[0-4]|1[0-9])[/._,\s-]+(january|february|march|april|may|june|july|august|september|october|november|december)[/._,\s-]+[0-9]{1,2}"
                              r"|(january|february|march|april|may|june|july|august|september|october|november|december)[/.,\s-]+[0-9]{1,2}[/.,\s-]+(20[0-9]{2}|2[0-4]|1[0-9])"
                              r"|(january|february|march|april|may|june|july|august|september|october|november|december)[/.,\s-]+(20[0-9]{2}|2[0-4]|1[0-9])"
                              r"|[0-9]{1,2}:[0-9]{1,2}:[0-9]{1,2}(\s[pa]m|)"
                              r"|[0-9]{1,2}:[0-9]{1,2}(\s[pa]m|))"
                              , flags=re.IGNORECASE)
    
    return date_pattern.sub("[DATE PLACEHOLDER]", text)

def compare_doc_text_2(cm_text, rev_text, cm_detailed_text, rev_detailed_text):
    sm = SequenceMatcher(None, cm_text.split(), rev_text.split())
    tag = 0
    i1 = 1
    i2 = 2
    j1 = 3
    j2 = 4
    added = []
    removed = []
    changed = []
    matched = []
    for x in sm.get_opcodes():

        if x[tag] == 'equal':
            matched.extend(cm_detailed_text.split()[x[i1]:x[i2]])

        elif x[tag] == 'insert':
            added.extend(rev_detailed_text.split()[x[j1]:x[j2]])

        elif x[tag] == 'delete':
            removed.extend(cm_detailed_text.split()[x[i1]:x[i2]])

        elif x[tag] == 'replace':
            cm_changed_text= cm_detailed_text.split()[x[i1]:x[i2]] 
            rev_changed_text = rev_detailed_text.split()[x[j1]:x[j2]]
            changed.append((cm_changed_text, rev_changed_text))

    return {
    "added": added,
    "removed": removed,
    "changed": changed,
    "matched":matched}

def compare_doc_text_only(cm_text, rev_text):
        sm = SequenceMatcher(None, cm_text.split(), rev_text.split())
        added = []
        removed = []
        changed = []
        for tag, i1, i2, j1, j2 in sm.get_opcodes():
            if tag == 'insert':
                added.extend(rev_text.split()[j1:j2])
            elif tag == 'delete':
                removed.extend(rev_text.split()[i1:i2])
            elif tag == 'replace':
                changed.append((rev_text.split()[i1:i2], cm_text.split()[j1:j2]))

        return {
            "added": added,
            "removed": removed,
            "changed": changed
        }

#DEPRESSED
def mark_changes(doc, changed_words_list, color):
    for page_index, page in enumerate(doc):
        for word in changed_words_list:
            if word.word_page == page_index:
                shape=page.new_shape()
                shape.draw_line((word.word_start, word.word_down), (word.word_end, word.word_down))
                shape.finish(color=color, width= 2)
                shape.commit()

#TEST ONLY
def mark_changes_manual(doc):
    for page_index, page in enumerate(doc):
                page.clean_contents()
                shape=page.new_shape()
                shape.draw_line((int(311), int(10)), (int(330), int(10)))
                shape.finish(color=(0,0,0), width= 2)
                shape.commit()

def mark_changes_2(doc, changed_words_list, color):
    for page_index, page in enumerate(doc):
        page.clean_contents()
        for word in changed_words_list:
            info = word.split("[WORDTEXT:]")
            word_info = info[0].split("-")
            word_page = int(word_info[0].split(":")[1])
            word_start = int(word_info[1].split(":")[1])
            word_end = int(word_info[2].split(":")[1])
            word_down = int(word_info[3].split(":")[1])
            if word_page == page_index:
                shape=page.new_shape()
                shape.draw_line((word_start, word_down), (word_end, word_down))
                shape.finish(color=color, width= 2)
                shape.commit()

def get_detailed_text(block_words_list):
    detailed_text = []
    for word in block_words_list:
        word_info = f"WP:{word.word_page}-WS:{int(word.word_start)}-WE:{int(word.word_end)}-WD:{int(word.word_down)}[WORDTEXT:]{word.word_text}"
        if word_info not in detailed_text:
            detailed_text.append(word_info)
    return " ".join(detailed_text)


def get_full_text(blocks_list):
    full_text = ""
    for block in blocks_list:
        full_text += block.block_original_text + "\n"
    return full_text.strip().lower()

def add_report_summary(doc, code_words_added, code_words_deleted, code_words_matched, code_words_changed):
    
    new_page_2 = doc.new_page(0)

    new_page_2.insert_text((50,50), "REVISION DOCUMENT", fontsize=8, fontname='hebo',color=(1, 0, 0))
    new_page_2.insert_text((150,800), "© <EMAIL>", fontsize=14, fontname='helv')

    new_page_1 = doc.new_page(0)

    new_page_1.insert_text((50,50), "CM DOCUMENT", fontsize=8, fontname='hebo', color=(1, 0, 0))
    new_page_1.insert_text((50,170), "Document Comparison Result", fontsize=20, fontname='hebo', color=(1, 0, 0))
    new_page_1.insert_text((50,190), "Report Summary", fontsize=14, fontname='helv', color=(1, 0, 0))
    new_page_1.insert_text((70,210), f"{code_words_added} word(s) added", fontsize=14, fontname='helv', color=(1, 0, 0))
    new_page_1.insert_text((70,230), f"{code_words_deleted} word(s) deleted", fontsize=14, fontname='helv', color=(1, 0, 0))
    new_page_1.insert_text((70,250), f"{code_words_changed} word(s) changed", fontsize=14, fontname='helv', color=(1, 0, 0))
    new_page_1.insert_text((70,270), f"{code_words_matched} word(s) matched", fontsize=14, fontname='helv', color=(1, 0, 0))
    new_page_1.insert_text((150,800), "© <EMAIL>", fontsize=14, fontname='helv')


def main(links):
    # Use local path for backend operations
    full_path = r"D:\PDF_compare\Abdelrahman_Maklad\_Tools\Blue_Space_logs\compare_documents"
    link_1, link_2, vendor_code = links.split("\t")[0].strip(), links.split("\t")[1].strip(), links.split("\t")[-1].strip()
    try:
        doc_1 = fitz.open(link_1.removesuffix('\n'))
        doc_2 = fitz.open(link_2.removesuffix('\n'))

        '''+++++++++++++++++++++++++++++++++++++++++++'''
        cm_words_list = extract_all_words(doc_1)
        cm_blocks_list = extract_all_blocks(doc_1)

        add_words_to_blocks(cm_blocks_list, cm_words_list)
        cm_block_words_list = [word for block in cm_blocks_list for word in block.block_words]

        cm_detailed_text = get_detailed_text(cm_block_words_list)
        cm_full_text = get_full_text(cm_blocks_list)

        '''+++++++++++++++++++++++++++++++++++++++++++'''

        rev_words_list = extract_all_words(doc_2)
        rev_blocks_list = extract_all_blocks(doc_2)

        add_words_to_blocks(rev_blocks_list, rev_words_list)
        rev_block_words_list = [word for block in rev_blocks_list for word in block.block_words]

        rev_detailed_text = get_detailed_text(rev_block_words_list)
        rev_full_text = get_full_text(rev_blocks_list)
        '''+++++++++++++++++++++++++++++++++++++++++++++++'''

        compare_result = compare_doc_text_2(cm_full_text, rev_full_text, cm_detailed_text, rev_detailed_text)

        cm_changed_words = compare_result["removed"]
        rev_changed_words = compare_result["added"]

        code_words_added = len(compare_result["added"])
        code_words_deleted = len(compare_result["removed"])
        code_words_matched = len(compare_result["matched"])
        code_words_changed = len(compare_result["changed"])
        
        for word_changed in compare_result["changed"]:
            cm_changed_words.extend(word_changed[0])
            rev_changed_words.extend(word_changed[-1])
        
        mark_changes_2(doc_1, cm_changed_words, (1, 0, 0))
        mark_changes_2(doc_2, rev_changed_words, (0, 0, 1))
        
        cm_page_count = count_of_pages(doc_1)
        rev_page_count = count_of_pages(doc_2)

        [doc_2.new_page() for x in range(cm_page_count-rev_page_count)]
        [doc_1.new_page() for x in range(rev_page_count-cm_page_count)]

        combined_pdf_name = f"compare_result_{random.randint(0, 9999999):07d}.pdf"
        combined_pdf_name = full_path + "\\" + combined_pdf_name

        # Create new PDF for combined output
        combined_doc = fitz.open()

        # Get max pages from both docs
        max_pages = max(len(doc_1), len(doc_2))

        # Add pages alternately
        for i in range(max_pages):
            if i < len(doc_1):
                combined_doc.insert_pdf(doc_1, from_page=i, to_page=i)
            if i < len(doc_2):
                combined_doc.insert_pdf(doc_2, from_page=i, to_page=i)

        add_report_summary(combined_doc, code_words_added, code_words_deleted, code_words_matched, code_words_changed)

        code_page_count = count_of_pages(combined_doc)
        
        combined_doc.save(combined_pdf_name)
        combined_doc.close()
        doc_1.close()
        doc_2.close()
        
        compare_code = f"{vendor_code.upper()}-{code_page_count}-{code_words_added}-{code_words_deleted}-{code_words_changed}-{code_words_matched}"
        return f"{links.strip()}\t{combined_pdf_name}\t{compare_code}" 
        
    except Exception as e:
        return f"{links.strip()}\t{str(e)}\tERROR" 


if __name__ == '__main__':
    
    input_path = sys.argv[1]
    output_path = sys.argv[2]
    mode = sys.argv[3]

    with open(input_path, "r") as input_file:
        links_list = input_file.read()
        links_list = json.loads(links_list)

    with open(output_path, "a", encoding='utf8') as output_file:
        pass    

    links_list = list(links_list.values())[:-1]

    if mode == "normal":
        for link in links_list:
            with open(output_path, "a", encoding='utf8') as output_file:
                output_file.write(main(link))
                output_file.write("\n")

    elif mode == "fast":
        one_time_count = 100
        with concurrent.futures.ProcessPoolExecutor(max_workers=7) as executor1:
            for i in range(1, len(links_list), one_time_count):
                batch_links = links_list[i:i + one_time_count]
                results = executor1.map(main, batch_links)
                for result in results:
                    with open(output_path, 'a', encoding='utf8') as of:
                        of.write(result)
                        of.write('\n')
                        print("Done")
