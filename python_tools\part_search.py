#!/usr/bin/env python3
"""
Part Search Tool (T005)
Advanced part number search with multiple status types:
- FOUND EXACT: Part found exactly as in document
- FOUND DIFFERENT FORMAT: Part found after removing special characters
- NOT FOUND: Part not found in document
- FOUND NON ALPHA: Part found with additional characters
"""

import sys
import json
import re
from datetime import datetime

class PartSearcher:
    def __init__(self):
        self.search_results = []
        
    def normalize_part_number(self, part):
        """Remove special characters and normalize part number"""
        # Remove spaces, dots, dashes, underscores
        normalized = re.sub(r'[\s\.\-_]+', '', part.upper())
        return normalized
    
    def search_part_in_text(self, part_number, text_content):
        """Search for part number in text with different matching strategies"""
        results = {
            'part_number': part_number,
            'status': 'NOT FOUND',
            'matches': [],
            'line_numbers': [],
            'match_details': []
        }
        
        lines = text_content.split('\n')
        part_upper = part_number.upper()
        part_normalized = self.normalize_part_number(part_number)
        
        for line_num, line in enumerate(lines, 1):
            line_upper = line.upper()
            line_normalized = self.normalize_part_number(line)
            
            # Strategy 1: Exact match
            if part_upper in line_upper:
                results['status'] = 'FOUND EXACT'
                results['matches'].append(line.strip())
                results['line_numbers'].append(line_num)
                results['match_details'].append({
                    'line_number': line_num,
                    'match_type': 'EXACT',
                    'line_content': line.strip(),
                    'matched_text': part_number
                })
                continue
            
            # Strategy 2: Normalized match (different format)
            if part_normalized and part_normalized in line_normalized:
                if results['status'] not in ['FOUND EXACT']:
                    results['status'] = 'FOUND DIFFERENT FORMAT'
                results['matches'].append(line.strip())
                results['line_numbers'].append(line_num)
                results['match_details'].append({
                    'line_number': line_num,
                    'match_type': 'DIFFERENT FORMAT',
                    'line_content': line.strip(),
                    'matched_text': part_normalized
                })
                continue
            
            # Strategy 3: Partial match with additional characters
            if self.find_partial_match(part_upper, line_upper):
                if results['status'] not in ['FOUND EXACT', 'FOUND DIFFERENT FORMAT']:
                    results['status'] = 'FOUND NON ALPHA'
                results['matches'].append(line.strip())
                results['line_numbers'].append(line_num)
                results['match_details'].append({
                    'line_number': line_num,
                    'match_type': 'NON ALPHA',
                    'line_content': line.strip(),
                    'matched_text': self.find_partial_match(part_upper, line_upper)
                })
        
        return results
    
    def find_partial_match(self, part, line):
        """Find partial matches where part is contained within larger strings"""
        # Look for the part number as a substring with word boundaries
        pattern = r'\b' + re.escape(part) + r'\w*'
        matches = re.findall(pattern, line)
        return matches[0] if matches else None
    
    def search_parts_in_document(self, parts_list, document_content):
        """Search multiple parts in a document"""
        results = []
        
        for part in parts_list:
            part = part.strip()
            if not part:
                continue
                
            result = self.search_part_in_text(part, document_content)
            results.append(result)
        
        return results
    
    def generate_summary(self, results):
        """Generate summary statistics"""
        summary = {
            'total_parts': len(results),
            'found_exact': 0,
            'found_different_format': 0,
            'found_non_alpha': 0,
            'not_found': 0
        }
        
        for result in results:
            status = result['status']
            if status == 'FOUND EXACT':
                summary['found_exact'] += 1
            elif status == 'FOUND DIFFERENT FORMAT':
                summary['found_different_format'] += 1
            elif status == 'FOUND NON ALPHA':
                summary['found_non_alpha'] += 1
            else:
                summary['not_found'] += 1
        
        summary['found_total'] = summary['total_parts'] - summary['not_found']
        summary['success_rate'] = (summary['found_total'] / summary['total_parts'] * 100) if summary['total_parts'] > 0 else 0
        
        return summary

def process_part_search(input_data, output_file, mode):
    """Process part search requests"""
    searcher = PartSearcher()
    all_results = []
    
    total_items = len(input_data)
    print(f"Starting part search for {total_items} items in {mode} mode...")
    
    for i, (line_num, content) in enumerate(input_data.items(), 1):
        content = content.strip()
        
        if not content:
            continue
        
        print(f"Processing item {i}/{total_items}")
        
        # Parse input - could be single part or document with parts list
        if content.startswith('PARTS:'):
            # Format: "PARTS: part1,part2,part3 | DOCUMENT: document_content"
            parts_section, doc_section = content.split(' | DOCUMENT: ', 1)
            parts_list = parts_section.replace('PARTS: ', '').split(',')
            document_content = doc_section
            
            results = searcher.search_parts_in_document(parts_list, document_content)
            
        elif '|' in content:
            # Format: "part_number | document_content"
            part_number, document_content = content.split(' | ', 1)
            results = [searcher.search_part_in_text(part_number, document_content)]
            
        else:
            # Assume it's just a part number to search in a default document
            results = [{
                'part_number': content,
                'status': 'NOT FOUND',
                'error': 'No document content provided for search'
            }]
        
        # Add metadata
        for result in results:
            result['line_number'] = line_num
            result['processing_order'] = i
            result['processing_timestamp'] = datetime.now().isoformat()
        
        all_results.extend(results)
        
        # Write intermediate results
        if i % 10 == 0 or i == total_items:
            write_results(all_results, output_file, searcher)
            if mode == 'fast' and i >= 20:  # Limit in fast mode
                print(f"Fast mode: Stopping after {i} items")
                break
    
    print(f"Part search completed. Results written to {output_file}")
    return all_results

def write_results(results, output_file, searcher):
    """Write search results to output file"""
    try:
        summary = searcher.generate_summary(results)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("AUTOSPACE PART SEARCH RESULTS\n")
            f.write("=" * 80 + "\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Total parts searched: {summary['total_parts']}\n")
            f.write(f"Success rate: {summary['success_rate']:.1f}%\n")
            f.write("=" * 80 + "\n\n")
            
            # Summary section
            f.write("SEARCH SUMMARY:\n")
            f.write("-" * 40 + "\n")
            f.write(f"FOUND EXACT: {summary['found_exact']}\n")
            f.write(f"FOUND DIFFERENT FORMAT: {summary['found_different_format']}\n")
            f.write(f"FOUND NON ALPHA: {summary['found_non_alpha']}\n")
            f.write(f"NOT FOUND: {summary['not_found']}\n")
            f.write("\n" + "=" * 80 + "\n\n")
            
            # Detailed results
            f.write("DETAILED RESULTS:\n")
            f.write("=" * 80 + "\n")
            
            for result in results:
                f.write(f"Part Number: {result['part_number']}\n")
                f.write(f"Status: {result['status']}\n")
                
                if 'error' in result:
                    f.write(f"Error: {result['error']}\n")
                elif result['status'] != 'NOT FOUND':
                    f.write(f"Found in {len(result['line_numbers'])} locations\n")
                    f.write("Match Details:\n")
                    
                    for detail in result.get('match_details', []):
                        f.write(f"  Line {detail['line_number']}: {detail['match_type']}\n")
                        f.write(f"    Content: {detail['line_content'][:100]}...\n")
                        f.write(f"    Matched: {detail['matched_text']}\n")
                
                f.write("\n" + "-" * 80 + "\n\n")
                
    except Exception as e:
        print(f"Error writing results: {e}")

def main():
    """Main function"""
    if len(sys.argv) != 4:
        print("Usage: python part_search.py <input_json> <output_file> <mode>")
        sys.exit(1)
    
    input_json_path = sys.argv[1]
    output_file = sys.argv[2]
    mode = sys.argv[3]
    
    try:
        # Load input data
        with open(input_json_path, 'r') as f:
            input_data = json.load(f)
        
        # Process part search
        process_part_search(input_data, output_file, mode)
        
        # Clear input file to signal completion
        with open(input_json_path, 'w') as f:
            json.dump({}, f)
            
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
