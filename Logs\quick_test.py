#!/usr/bin/env python3
"""
Quick test to verify the authentication fix
"""

import requests
import sys

def test_authentication_flow():
    """Test the authentication flow"""
    base_url = "http://localhost:5000"
    
    print("Testing AutoSpace Authentication Flow")
    print("=" * 50)
    
    # Test 1: Health check
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Server is running - {data.get('service')} v{data.get('version')}")
        else:
            print(f"✗ Health check failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"✗ Cannot connect to server: {e}")
        print("Make sure the server is running with: python backend.py")
        return False
    
    # Test 2: Root URL should redirect to login (for browser) or return 401 (for API)
    try:
        response = requests.get(f"{base_url}/", allow_redirects=False)
        if response.status_code == 302:
            print("✓ Root URL redirects to login (browser behavior)")
        elif response.status_code == 401:
            print("✓ Root URL returns 401 for unauthenticated requests")
        else:
            print(f"? Unexpected response from root URL: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"✗ Error testing root URL: {e}")
    
    # Test 3: Login page should be accessible
    try:
        response = requests.get(f"{base_url}/login")
        if response.status_code == 200 and "AUTOSPACE" in response.text:
            print("✓ Login page is accessible")
        else:
            print(f"✗ Login page issue: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"✗ Error accessing login page: {e}")
        return False
    
    # Test 4: Login with credentials
    session = requests.Session()
    try:
        login_data = {"username": "admin", "password": "admin"}
        response = session.post(
            f"{base_url}/login",
            json=login_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('message') == 'Login successful':
                print("✓ Login successful with admin/admin")
            else:
                print(f"✗ Unexpected login response: {data}")
                return False
        else:
            print(f"✗ Login failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"✗ Error during login: {e}")
        return False
    
    # Test 5: Access protected page after login
    try:
        response = session.get(f"{base_url}/")
        if response.status_code == 200 and "AUTOSPACE" in response.text:
            print("✓ Main page accessible after login")
        else:
            print(f"✗ Cannot access main page after login: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"✗ Error accessing main page: {e}")
        return False
    
    # Test 6: API endpoints work after login
    try:
        response = session.get(f"{base_url}/api/user")
        if response.status_code == 200:
            data = response.json()
            if data.get('username') == 'admin':
                print("✓ User API works after login")
            else:
                print(f"? Unexpected user data: {data}")
        else:
            print(f"✗ User API failed: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"✗ Error testing user API: {e}")
    
    print("\n" + "=" * 50)
    print("✅ Authentication flow is working correctly!")
    print("\nTo use AutoSpace:")
    print("1. Go to http://localhost:5000")
    print("2. You'll be redirected to login page")
    print("3. Login with: admin / admin")
    print("4. You'll be redirected to the main application")
    
    return True

if __name__ == "__main__":
    success = test_authentication_flow()
    sys.exit(0 if success else 1)
