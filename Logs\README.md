# AutoSpace - Document Processing Platform

AutoSpace is a comprehensive web-based document processing platform built with Flask (Python backend) and vanilla JavaScript frontend. The application provides a suite of specialized tools for document analysis, comparison, extraction, and search operations.

## Features

### Core Architecture
- **Backend**: Flask web server with session management
- **Frontend**: Responsive single-page application with tabbed navigation
- **Authentication**: User authentication and session management
- **Real-time Monitoring**: Session progress tracking with file-based persistence

### Tool Categories

#### 1. Extraction Tools (CAT001)
- **Extractor (T001)**: Line-by-line text extraction from documents
- **Meta Data (T003)**: Extracts document metadata (title, creation date, modification date, author)

#### 2. Comparison Tools (CAT002)
- **Acrobat Sim (T002)**: Document comparison with change highlighting and summary generation
- **Equal Manual (T006)**: Advanced document similarity detection with date-ignore options
- **Image Compare (T008)**: Image comparison between documents with position-based change detection
- **Catalogue Compare (T009)**: Document comparison ignoring date differences

#### 3. Search Tools (CAT003)
- **Keyword Search (T004)**: Searches for keywords and returns matching lines
- **Part Search (T005)**: Advanced part number search with multiple status types

#### 4. Analysis Tools (CAT004)
- **Magic (T007)**: Complex workflow tool that extracts keyword-based lines, compares changes, and maps features using vendor-specific databases

## Installation

### Prerequisites
- Python 3.7 or higher
- Flask
- Modern web browser

### Setup
1. Clone or download the project files
2. Install Flask:
   ```bash
   pip install flask
   ```

3. Navigate to the project directory:
   ```bash
   cd AutoSpace
   ```

4. Run the application:
   ```bash
   python backend.py
   ```

5. Open your browser and navigate to:
   ```
   http://localhost:5000
   ```

## Usage

### First Time Setup
1. When you first access the application, you'll be redirected to the login page
2. Use the default credentials:
   - **Username**: admin
   - **Password**: admin

### Using the Platform
1. **Select a Tool Category**: Click on one of the four categories in the sidebar (Extraction, Comparison, Search, Analysis)
2. **Choose a Tool**: Click on a tool card to see its description and options
3. **Upload a File**: Click the "Upload" button and select your document
4. **Configure Options**: Choose between Fast or Normal processing mode
5. **Run the Tool**: Click "Run Tool" to start processing
6. **Monitor Progress**: Watch the session status in the "Session Management" section
7. **Download Results**: Once complete, click the download button to get your results

### Session Management
- **Active Sessions**: Shows currently running processes with progress indicators
- **Completed Sessions**: Shows finished sessions with download links
- **Real-time Updates**: Sessions update automatically every 5 seconds

## File Structure

```
AutoSpace/
├── backend.py                 # Main Flask application
├── static/
│   ├── data.js               # Tool definitions and categories
│   └── styles.css            # Main stylesheet
├── templates/
│   ├── index.html            # Main application interface
│   └── login.html            # Authentication page
├── python_tools/             # Processing tool modules
│   ├── extractor.py
│   ├── acrobat_sim.py
│   ├── equal_manual.py
│   ├── keyword_search.py
│   ├── meta_data.py
│   ├── part_search.py
│   ├── magic.py
│   ├── image_compare.py
│   └── catalogue_compare.py
├── AutoSpace_output_files/   # Generated output directory
├── sessions.txt              # Session tracking file
├── users.txt                 # User management file
├── test_autospace.py         # System test script
└── README.md                 # This file
```

## API Endpoints

### Authentication
- `POST /login` - User authentication
- `GET /logout` - Session termination

### File Processing
- `POST /upload` - File upload and session creation
- `POST /runtool` - Tool execution initiation

### Session Management
- `GET /api/sessions` - User session retrieval
- `GET /api/sessions/{id}/progress` - Real-time progress tracking
- `GET /api/user` - Current user information

### File Downloads
- `GET /download/{filename}` - Output file download

### Tools
- `GET /api/tools` - Available tools list

## Testing

Run the included test script to validate system functionality:

```bash
python test_autospace.py
```

The test script will verify:
- User authentication
- API endpoints
- File upload and processing
- Session management
- Tool availability

## Tool Input Formats

### Single Document Tools
- **Extractor**: Plain text file with content to extract
- **Meta Data**: Any file for metadata extraction
- **Keyword Search**: Text content with keywords to search

### Comparison Tools
- **Format**: `DOC1: content1 | DOC2: content2`
- **Example**: `DOC1: First document content | DOC2: Second document content`

### Search Tools
- **Part Search**: `part_number | document_content` or `PARTS: part1,part2 | DOCUMENT: content`
- **Keyword Search**: Text content with keywords

### Analysis Tools
- **Magic**: Requires keywords.xlsx and mapping.xlsx files (vendor-specific processing)

## Configuration

### Processing Modes
- **Fast Mode**: Limited processing for quick results
- **Normal Mode**: Full processing with complete analysis

### Session Persistence
- Sessions are stored in `sessions.txt`
- User data is stored in `users.txt`
- Output files are saved in `AutoSpace_output_files/`

## Security Features
- User authentication required for all operations
- Session-based access control
- File path validation for downloads
- Input sanitization

## Browser Compatibility
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## Troubleshooting

### Common Issues
1. **Login Page Not Loading**: Ensure Flask server is running on port 5000
2. **File Upload Fails**: Check file permissions and available disk space
3. **Tool Processing Stuck**: Check python_tools directory and file permissions
4. **Session Not Updating**: Refresh the page or check network connectivity

### Debug Mode
To enable debug mode, modify `backend.py`:
```python
app.run(host='0.0.0.0', port=5000, debug=True)
```

## Contributing
1. Follow the existing code structure
2. Add new tools to the `python_tools/` directory
3. Update `data.js` with new tool definitions
4. Test thoroughly before deployment

## License
© <EMAIL> All rights reserved.

## Support
For issues and questions, please check the troubleshooting section or review the test script output for specific error messages.
