#!/usr/bin/env python3
"""
Image Compare Tool (T008)
Image comparison between documents with:
- Position-based change detection
- Page-by-page image analysis
- Visual difference highlighting
- Image count and placement tracking
- Detailed image change reports
"""

import sys
import json
import os
import re
from datetime import datetime

class ImageComparer:
    def __init__(self):
        self.image_patterns = [
            r'\.jpg', r'\.jpeg', r'\.png', r'\.gif', r'\.bmp', r'\.tiff', r'\.svg',
            r'image\s*\d+', r'figure\s*\d+', r'diagram\s*\d+', r'chart\s*\d+',
            r'<img[^>]*>', r'\[image\]', r'\[figure\]', r'\[diagram\]'
        ]
        
    def extract_image_references(self, content, document_name):
        """Extract image references from document content"""
        images = []
        lines = content.split('\n')
        
        for line_num, line in enumerate(lines, 1):
            line_lower = line.lower()
            
            for pattern in self.image_patterns:
                matches = re.finditer(pattern, line_lower)
                for match in matches:
                    images.append({
                        'document': document_name,
                        'line_number': line_num,
                        'line_content': line.strip(),
                        'image_reference': match.group(),
                        'position_start': match.start(),
                        'position_end': match.end(),
                        'context': self.get_context(lines, line_num - 1, 2)
                    })
        
        return images
    
    def get_context(self, lines, line_index, context_size):
        """Get context lines around the image reference"""
        start = max(0, line_index - context_size)
        end = min(len(lines), line_index + context_size + 1)
        
        context_lines = []
        for i in range(start, end):
            prefix = ">>> " if i == line_index else "    "
            context_lines.append(f"{prefix}Line {i+1}: {lines[i].strip()}")
        
        return '\n'.join(context_lines)
    
    def analyze_image_positions(self, images):
        """Analyze image positions and create position map"""
        position_map = {}
        
        for img in images:
            page_key = f"page_{img['line_number'] // 50 + 1}"  # Approximate page
            if page_key not in position_map:
                position_map[page_key] = []
            
            position_map[page_key].append({
                'line': img['line_number'],
                'reference': img['image_reference'],
                'document': img['document']
            })
        
        return position_map
    
    def compare_documents(self, doc1_content, doc2_content, doc1_name, doc2_name):
        """Compare images between two documents"""
        # Extract images from both documents
        images1 = self.extract_image_references(doc1_content, doc1_name)
        images2 = self.extract_image_references(doc2_content, doc2_name)
        
        # Analyze positions
        pos_map1 = self.analyze_image_positions(images1)
        pos_map2 = self.analyze_image_positions(images2)
        
        # Compare image counts
        comparison_result = {
            'document1': {
                'name': doc1_name,
                'image_count': len(images1),
                'images': images1,
                'position_map': pos_map1
            },
            'document2': {
                'name': doc2_name,
                'image_count': len(images2),
                'images': images2,
                'position_map': pos_map2
            },
            'comparison': self.perform_comparison(images1, images2, pos_map1, pos_map2),
            'summary': self.generate_comparison_summary(images1, images2)
        }
        
        return comparison_result
    
    def perform_comparison(self, images1, images2, pos_map1, pos_map2):
        """Perform detailed comparison between image sets"""
        changes = {
            'added_images': [],
            'removed_images': [],
            'moved_images': [],
            'position_changes': [],
            'page_changes': []
        }
        
        # Create reference sets for comparison
        refs1 = {img['image_reference']: img for img in images1}
        refs2 = {img['image_reference']: img for img in images2}
        
        # Find added images (in doc2 but not in doc1)
        for ref, img in refs2.items():
            if ref not in refs1:
                changes['added_images'].append(img)
        
        # Find removed images (in doc1 but not in doc2)
        for ref, img in refs1.items():
            if ref not in refs2:
                changes['removed_images'].append(img)
        
        # Find moved/repositioned images
        for ref in refs1:
            if ref in refs2:
                img1 = refs1[ref]
                img2 = refs2[ref]
                
                if img1['line_number'] != img2['line_number']:
                    changes['moved_images'].append({
                        'reference': ref,
                        'old_position': img1['line_number'],
                        'new_position': img2['line_number'],
                        'old_context': img1['context'],
                        'new_context': img2['context']
                    })
        
        # Compare page distributions
        all_pages1 = set(pos_map1.keys())
        all_pages2 = set(pos_map2.keys())
        
        for page in all_pages1.union(all_pages2):
            count1 = len(pos_map1.get(page, []))
            count2 = len(pos_map2.get(page, []))
            
            if count1 != count2:
                changes['page_changes'].append({
                    'page': page,
                    'old_count': count1,
                    'new_count': count2,
                    'change': count2 - count1
                })
        
        return changes
    
    def generate_comparison_summary(self, images1, images2):
        """Generate summary statistics for the comparison"""
        return {
            'total_images_doc1': len(images1),
            'total_images_doc2': len(images2),
            'image_count_change': len(images2) - len(images1),
            'has_image_changes': len(images1) != len(images2),
            'change_percentage': abs(len(images2) - len(images1)) / max(len(images1), 1) * 100
        }

def process_image_comparison(input_data, output_file, mode):
    """Process image comparison requests"""
    comparer = ImageComparer()
    all_results = []
    
    total_items = len(input_data)
    print(f"Starting image comparison for {total_items} items in {mode} mode...")
    
    for i, (line_num, content) in enumerate(input_data.items(), 1):
        content = content.strip()
        
        if not content:
            continue
        
        print(f"Processing item {i}/{total_items}")
        
        try:
            # Parse input format: "DOC1: content1 | DOC2: content2"
            if ' | DOC2: ' in content:
                doc1_part, doc2_part = content.split(' | DOC2: ', 1)
                doc1_content = doc1_part.replace('DOC1: ', '')
                doc2_content = doc2_part
                
                result = comparer.compare_documents(
                    doc1_content, doc2_content, 
                    f"Document_1_Line_{line_num}", 
                    f"Document_2_Line_{line_num}"
                )
                
            elif content.startswith('ANALYZE: '):
                # Single document analysis
                doc_content = content.replace('ANALYZE: ', '')
                images = comparer.extract_image_references(doc_content, f"Document_Line_{line_num}")
                
                result = {
                    'analysis_type': 'single_document',
                    'document_name': f"Document_Line_{line_num}",
                    'image_count': len(images),
                    'images': images,
                    'position_map': comparer.analyze_image_positions(images)
                }
                
            else:
                result = {
                    'error': 'Invalid input format. Use "DOC1: content1 | DOC2: content2" or "ANALYZE: content"',
                    'input_content': content[:100] + '...' if len(content) > 100 else content
                }
        
        except Exception as e:
            result = {
                'error': f"Processing error: {str(e)}",
                'input_content': content[:100] + '...' if len(content) > 100 else content
            }
        
        # Add metadata
        result['line_number'] = line_num
        result['processing_order'] = i
        result['processing_timestamp'] = datetime.now().isoformat()
        
        all_results.append(result)
        
        # Write intermediate results
        if i % 5 == 0 or i == total_items:
            write_results(all_results, output_file)
            if mode == 'fast' and i >= 10:  # Limit in fast mode
                print(f"Fast mode: Stopping after {i} items")
                break
    
    print(f"Image comparison completed. Results written to {output_file}")
    return all_results

def write_results(results, output_file):
    """Write image comparison results to output file"""
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("AUTOSPACE IMAGE COMPARISON RESULTS\n")
            f.write("=" * 80 + "\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Total comparisons: {len(results)}\n")
            f.write("=" * 80 + "\n\n")
            
            # Summary statistics
            comparisons = [r for r in results if 'comparison' in r]
            analyses = [r for r in results if 'analysis_type' in r]
            errors = [r for r in results if 'error' in r]
            
            f.write("SUMMARY:\n")
            f.write("-" * 40 + "\n")
            f.write(f"Document comparisons: {len(comparisons)}\n")
            f.write(f"Single document analyses: {len(analyses)}\n")
            f.write(f"Errors: {len(errors)}\n")
            f.write("\n")
            
            if comparisons:
                total_changes = sum(1 for r in comparisons if r.get('summary', {}).get('has_image_changes', False))
                f.write(f"Documents with image changes: {total_changes}\n")
            
            f.write("\n" + "=" * 80 + "\n\n")
            
            # Detailed results
            f.write("DETAILED RESULTS:\n")
            f.write("=" * 80 + "\n")
            
            for result in results:
                f.write(f"Item #{result['line_number']}\n")
                f.write("-" * 60 + "\n")
                
                if 'error' in result:
                    f.write(f"ERROR: {result['error']}\n")
                    f.write(f"Input: {result.get('input_content', 'N/A')}\n")
                
                elif 'comparison' in result:
                    # Document comparison result
                    doc1 = result['document1']
                    doc2 = result['document2']
                    summary = result['summary']
                    changes = result['comparison']
                    
                    f.write(f"COMPARISON: {doc1['name']} vs {doc2['name']}\n")
                    f.write(f"Image count: {doc1['image_count']} -> {doc2['image_count']} (change: {summary['image_count_change']})\n")
                    f.write(f"Change percentage: {summary['change_percentage']:.1f}%\n")
                    f.write("\n")
                    
                    if changes['added_images']:
                        f.write(f"ADDED IMAGES ({len(changes['added_images'])}):\n")
                        for img in changes['added_images']:
                            f.write(f"  Line {img['line_number']}: {img['image_reference']}\n")
                        f.write("\n")
                    
                    if changes['removed_images']:
                        f.write(f"REMOVED IMAGES ({len(changes['removed_images'])}):\n")
                        for img in changes['removed_images']:
                            f.write(f"  Line {img['line_number']}: {img['image_reference']}\n")
                        f.write("\n")
                    
                    if changes['moved_images']:
                        f.write(f"MOVED IMAGES ({len(changes['moved_images'])}):\n")
                        for move in changes['moved_images']:
                            f.write(f"  {move['reference']}: Line {move['old_position']} -> {move['new_position']}\n")
                        f.write("\n")
                
                elif 'analysis_type' in result:
                    # Single document analysis
                    f.write(f"ANALYSIS: {result['document_name']}\n")
                    f.write(f"Total images found: {result['image_count']}\n")
                    
                    if result['images']:
                        f.write("Image references:\n")
                        for img in result['images']:
                            f.write(f"  Line {img['line_number']}: {img['image_reference']}\n")
                
                f.write("\n" + "=" * 80 + "\n\n")
                
    except Exception as e:
        print(f"Error writing results: {e}")

def main():
    """Main function"""
    if len(sys.argv) != 4:
        print("Usage: python image_compare.py <input_json> <output_file> <mode>")
        sys.exit(1)
    
    input_json_path = sys.argv[1]
    output_file = sys.argv[2]
    mode = sys.argv[3]
    
    try:
        # Load input data
        with open(input_json_path, 'r') as f:
            input_data = json.load(f)
        
        # Process image comparison
        process_image_comparison(input_data, output_file, mode)
        
        # Clear input file to signal completion
        with open(input_json_path, 'w') as f:
            json.dump({}, f)
            
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
