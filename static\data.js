// Tool Categories
const toolCategories = {
  CAT001: { name: "Extraction Tools", id: "extraction", icon: "📄" },
  CAT002: { name: "Comparison Tools", id: "comparison", icon: "🔍" },
  CAT003: { name: "Search Tools", id: "search", icon: "🔎" },
  CAT004: { name: "Analysis Tools", id: "analysis", icon: "📊" }
};

// Tools Data with proper categorization and IDs
const toolsData = [
  // CAT001 - Extraction Tools
  {
    id: "T001",
    name: "Extractor",
    description: `Line-by-line text extraction from documents.

Features:
• Extracts all text content line by line
• Preserves document structure
• Handles multiple document formats
• Fast processing mode available`,
    category: "CAT001",
    categoryId: "extraction",
    toolName: "extractor",
    sampleFile: "extractor_sample.txt"
  },
  {
    id: "T003",
    name: "Meta Data",
    description: `Extracts document metadata including:

• Document title
• Document creation date
• Document modification date
• Document author
• File size and format information
• Document properties and attributes`,
    category: "CAT001",
    categoryId: "extraction",
    toolName: "meta_data",
    sampleFile: "metadata_sample.txt"
  },

  // CAT002 - Comparison Tools
  {
    id: "T002",
    name: "Acrobat Sim",
    description: `Document comparison with advanced change detection:

Features:
• Comprehensive document comparison
• Change highlighting and summary generation
• Side-by-side difference visualization
• Detailed change reports
• Support for multiple document formats`,
    category: "CAT002",
    categoryId: "comparison",
    toolName: "acrobat_sim",
    sampleFile: "acrobat_sim_sample.txt"
  },
  {
    id: "T006",
    name: "Equal Manual",
    description: `Advanced document similarity detection with:

1. Identify documents that are identical
2. Identify documents identical when dates are ignored
3. Detect potential reach or lifecycle changes
4. Identify potentially unsearchable documents
5. Detect image changes based on image count
6. Group documents by characteristics:
   • Number of characters in document
   • Number of lines changed between versions`,
    category: "CAT002",
    categoryId: "comparison",
    toolName: "equal_manual",
    sampleFile: "equal_manual_sample.txt"
  },
  {
    id: "T008",
    name: "Image Compare",
    description: `Image comparison between documents with:

Features:
• Position-based change detection
• Page-by-page image analysis
• Visual difference highlighting
• Image count and placement tracking
• Detailed image change reports`,
    category: "CAT002",
    categoryId: "comparison",
    toolName: "image_compare",
    sampleFile: "image_compare_sample.txt"
  },
  {
    id: "T009",
    name: "Catalogue Compare",
    description: `Document comparison ignoring date differences:

Features:
• Smart date filtering
• Content-focused comparison
• Change page identification
• Catalog-specific optimization
• Batch processing support`,
    category: "CAT002",
    categoryId: "comparison",
    toolName: "catalogue_compare",
    sampleFile: "catalogue_compare_sample.txt"
  },

  // CAT003 - Search Tools
  {
    id: "T004",
    name: "Keyword Search",
    description: `Advanced keyword search functionality:

Features:
• Search for specific keywords in documents
• Return matching lines with context
• Case-sensitive and case-insensitive options
• Multiple keyword support
• Export search results`,
    category: "CAT003",
    categoryId: "search",
    toolName: "keyword_search",
    sampleFile: "keyword_search_sample.txt"
  },
  {
    id: "T005",
    name: "Part Search",
    description: `Advanced part number search with multiple status types:

Status Types:
• FOUND EXACT: Part found exactly as in document
• FOUND DIFFERENT FORMAT: Part found after removing special characters
• NOT FOUND: Part not found in document
• FOUND NON ALPHA: Part found with additional characters

Features:
• Single part or batch part list processing
• Flexible format matching
• Detailed status reporting`,
    category: "CAT003",
    categoryId: "search",
    toolName: "part_search",
    sampleFile: "part_search_sample.txt"
  },

  // CAT004 - Analysis Tools
  {
    id: "T007",
    name: "Magic",
    description: `Complex workflow tool with vendor-specific processing:

Workflow:
1. Extract lines containing keywords from "keywords.xlsx"
2. Compare extracted lines to highlight changes
3. Map keywords to features using "mapping.xlsx"
4. Generate vendor-specific output files

Requirements:
• Input file ("input.xlsx") with vendor-grouped entries
• Vendor-specific keyword files
• Vendor-specific mapping databases

** Note: Each supplier must have associated keyword and mapping files`,
    category: "CAT004",
    categoryId: "analysis",
    toolName: "magic",
    sampleFile: "magic_sample.xlsx"
  }
];

// Export the data for ES modules
export { toolsData, toolCategories };
export default toolsData;