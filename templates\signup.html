<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AutoSpace - Sign Up</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;600;700&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Montserrat', sans-serif;
        }

        .signup-container {
            background: white;
            padding: 3rem;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 450px;
            text-align: center;
        }

        .signup-header {
            margin-bottom: 2rem;
        }

        .signup-header h1 {
            color: #333;
            font-size: 2.5rem;
            font-weight: 700;
            letter-spacing: 2px;
            margin-bottom: 0.5rem;
        }

        .signup-header p {
            color: #666;
            font-size: 1rem;
            font-weight: 300;
        }

        .signup-form {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .form-group {
            position: relative;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 600;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .form-group input {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-group input.valid {
            border-color: #28a745;
            background: #f8fff9;
        }

        .form-group input.invalid {
            border-color: #dc3545;
            background: #fff8f8;
        }

        .form-group i {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            margin-top: 12px;
        }

        .form-group .validation-icon {
            right: 3rem;
            color: #28a745;
            display: none;
        }

        .form-group .validation-icon.show {
            display: block;
        }

        .form-group .validation-icon.error {
            color: #dc3545;
        }

        .password-strength {
            margin-top: 0.5rem;
            font-size: 0.8rem;
        }

        .strength-bar {
            height: 4px;
            background: #e1e5e9;
            border-radius: 2px;
            margin-top: 0.25rem;
            overflow: hidden;
        }

        .strength-fill {
            height: 100%;
            transition: all 0.3s ease;
            border-radius: 2px;
        }

        .strength-weak { background: #dc3545; width: 25%; }
        .strength-fair { background: #ffc107; width: 50%; }
        .strength-good { background: #17a2b8; width: 75%; }
        .strength-strong { background: #28a745; width: 100%; }

        .email-hint {
            font-size: 0.8rem;
            color: #666;
            margin-top: 0.25rem;
            font-style: italic;
        }

        .signup-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 1rem;
        }

        .signup-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .signup-button:active {
            transform: translateY(0);
        }

        .signup-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .error-message {
            background: #fee;
            color: #c33;
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid #c33;
            margin-bottom: 1rem;
            display: none;
        }

        .success-message {
            background: #efe;
            color: #3c3;
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid #3c3;
            margin-bottom: 1rem;
            display: none;
        }

        .loading {
            display: none;
            margin-top: 1rem;
        }

        .loading i {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .login-link {
            margin-top: 2rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #17a2b8;
        }

        .login-link h4 {
            color: #17a2b8;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .login-link p {
            color: #666;
            font-size: 0.9rem;
            margin: 0;
        }

        .login-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }

        .login-link a:hover {
            text-decoration: underline;
        }

        .requirements {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin-top: 1rem;
            text-align: left;
        }

        .requirements h4 {
            color: #333;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .requirements ul {
            margin: 0;
            padding-left: 1.5rem;
            font-size: 0.8rem;
            color: #666;
        }

        .requirements li {
            margin-bottom: 0.25rem;
        }

        .requirements li.valid {
            color: #28a745;
        }

        .requirements li.invalid {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="signup-container">
        <div class="signup-header">
            <h1>AUTOSPACE</h1>
            <p>Create Your Account</p>
        </div>

        <div class="error-message" id="error-message"></div>
        <div class="success-message" id="success-message"></div>

        <form class="signup-form" id="signup-form">
            <div class="form-group">
                <label for="email">Email Address</label>
                <input type="email" id="email" name="email" required placeholder="<EMAIL>">
                <i class="fas fa-envelope"></i>
                <i class="fas fa-check validation-icon" id="email-valid"></i>
                <div class="email-hint">Must be a valid @siliconexpert.com email address</div>
            </div>

            <div class="form-group">
                <label for="full-name">Full Name</label>
                <input type="text" id="full-name" name="full_name" required placeholder="Enter your full name">
                <i class="fas fa-user"></i>
                <i class="fas fa-check validation-icon" id="name-valid"></i>
            </div>

            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required placeholder="Create a strong password">
                <i class="fas fa-lock"></i>
                <i class="fas fa-check validation-icon" id="password-valid"></i>
                <div class="password-strength">
                    <div class="strength-bar">
                        <div class="strength-fill" id="strength-fill"></div>
                    </div>
                    <span id="strength-text">Password strength</span>
                </div>
            </div>

            <div class="form-group">
                <label for="confirm-password">Confirm Password</label>
                <input type="password" id="confirm-password" name="confirm_password" required placeholder="Confirm your password">
                <i class="fas fa-lock"></i>
                <i class="fas fa-check validation-icon" id="confirm-valid"></i>
            </div>

            <div class="requirements">
                <h4>Password Requirements:</h4>
                <ul>
                    <li id="req-length">At least 8 characters</li>
                    <li id="req-uppercase">One uppercase letter</li>
                    <li id="req-lowercase">One lowercase letter</li>
                    <li id="req-number">One number</li>
                    <li id="req-special">One special character</li>
                </ul>
            </div>

            <button type="submit" class="signup-button" id="signup-button">
                <span id="button-text">Create Account</span>
                <div class="loading" id="loading">
                    <i class="fas fa-spinner"></i> Creating Account...
                </div>
            </button>
        </form>

        <div class="login-link">
            <h4>Already Have an Account?</h4>
            <p><a href="/login">Sign in here</a></p>
        </div>

        <div class="login-link" style="border-left-color: #667eea;">
            <h4 style="color: #667eea;">Explore</h4>
            <p>
                <a href="/home">Dashboard</a> |
                <a href="/about">About</a>
            </p>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const signupForm = document.getElementById('signup-form');
            const emailInput = document.getElementById('email');
            const nameInput = document.getElementById('full-name');
            const passwordInput = document.getElementById('password');
            const confirmPasswordInput = document.getElementById('confirm-password');
            const signupButton = document.getElementById('signup-button');
            const buttonText = document.getElementById('button-text');
            const loading = document.getElementById('loading');
            const errorMessage = document.getElementById('error-message');
            const successMessage = document.getElementById('success-message');

            // Validation functions
            function validateEmail(email) {
                const emailPattern = /^[a-zA-Z0-9._%+-]+@siliconexpert\.com$/;
                return emailPattern.test(email);
            }

            function validatePassword(password) {
                const requirements = {
                    length: password.length >= 8,
                    uppercase: /[A-Z]/.test(password),
                    lowercase: /[a-z]/.test(password),
                    number: /\d/.test(password),
                    special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
                };
                return requirements;
            }

            function getPasswordStrength(password) {
                const reqs = validatePassword(password);
                const score = Object.values(reqs).filter(Boolean).length;
                
                if (score < 2) return { strength: 'weak', text: 'Weak' };
                if (score < 4) return { strength: 'fair', text: 'Fair' };
                if (score < 5) return { strength: 'good', text: 'Good' };
                return { strength: 'strong', text: 'Strong' };
            }

            // Real-time validation
            emailInput.addEventListener('input', function() {
                const email = this.value;
                const isValid = validateEmail(email);
                const validIcon = document.getElementById('email-valid');
                
                if (email) {
                    if (isValid) {
                        this.classList.add('valid');
                        this.classList.remove('invalid');
                        validIcon.classList.add('show');
                        validIcon.classList.remove('error');
                    } else {
                        this.classList.add('invalid');
                        this.classList.remove('valid');
                        validIcon.classList.add('show', 'error');
                    }
                } else {
                    this.classList.remove('valid', 'invalid');
                    validIcon.classList.remove('show');
                }
            });

            nameInput.addEventListener('input', function() {
                const name = this.value.trim();
                const validIcon = document.getElementById('name-valid');
                
                if (name.length >= 2) {
                    this.classList.add('valid');
                    this.classList.remove('invalid');
                    validIcon.classList.add('show');
                    validIcon.classList.remove('error');
                } else if (name.length > 0) {
                    this.classList.add('invalid');
                    this.classList.remove('valid');
                    validIcon.classList.add('show', 'error');
                } else {
                    this.classList.remove('valid', 'invalid');
                    validIcon.classList.remove('show');
                }
            });

            passwordInput.addEventListener('input', function() {
                const password = this.value;
                const reqs = validatePassword(password);
                const strength = getPasswordStrength(password);
                const validIcon = document.getElementById('password-valid');
                const strengthFill = document.getElementById('strength-fill');
                const strengthText = document.getElementById('strength-text');

                // Update strength indicator
                strengthFill.className = `strength-fill strength-${strength.strength}`;
                strengthText.textContent = `Password strength: ${strength.text}`;

                // Update requirements
                Object.keys(reqs).forEach(req => {
                    const element = document.getElementById(`req-${req}`);
                    if (element) {
                        element.className = reqs[req] ? 'valid' : 'invalid';
                    }
                });

                // Update validation icon
                const allValid = Object.values(reqs).every(Boolean);
                if (password) {
                    if (allValid) {
                        this.classList.add('valid');
                        this.classList.remove('invalid');
                        validIcon.classList.add('show');
                        validIcon.classList.remove('error');
                    } else {
                        this.classList.add('invalid');
                        this.classList.remove('valid');
                        validIcon.classList.add('show', 'error');
                    }
                } else {
                    this.classList.remove('valid', 'invalid');
                    validIcon.classList.remove('show');
                }

                // Check confirm password match
                checkPasswordMatch();
            });

            confirmPasswordInput.addEventListener('input', checkPasswordMatch);

            function checkPasswordMatch() {
                const password = passwordInput.value;
                const confirmPassword = confirmPasswordInput.value;
                const validIcon = document.getElementById('confirm-valid');

                if (confirmPassword) {
                    if (password === confirmPassword && password.length > 0) {
                        confirmPasswordInput.classList.add('valid');
                        confirmPasswordInput.classList.remove('invalid');
                        validIcon.classList.add('show');
                        validIcon.classList.remove('error');
                    } else {
                        confirmPasswordInput.classList.add('invalid');
                        confirmPasswordInput.classList.remove('valid');
                        validIcon.classList.add('show', 'error');
                    }
                } else {
                    confirmPasswordInput.classList.remove('valid', 'invalid');
                    validIcon.classList.remove('show');
                }
            }

            // Form submission
            signupForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                
                const email = emailInput.value.trim();
                const fullName = nameInput.value.trim();
                const password = passwordInput.value;
                const confirmPassword = confirmPasswordInput.value;

                // Validate all fields
                if (!validateEmail(email)) {
                    showError('Please enter a valid @siliconexpert.com email address');
                    return;
                }

                if (fullName.length < 2) {
                    showError('Please enter your full name (at least 2 characters)');
                    return;
                }

                const passwordReqs = validatePassword(password);
                if (!Object.values(passwordReqs).every(Boolean)) {
                    showError('Password does not meet all requirements');
                    return;
                }

                if (password !== confirmPassword) {
                    showError('Passwords do not match');
                    return;
                }

                // Show loading state
                setLoadingState(true);
                hideMessages();

                try {
                    const response = await fetch('/signup', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ 
                            email, 
                            full_name: fullName, 
                            password 
                        })
                    });

                    const data = await response.json();

                    if (response.ok) {
                        showSuccess('Account created successfully! Redirecting to login...');
                        
                        setTimeout(() => {
                            window.location.href = '/login?message=account_created';
                        }, 2000);
                    } else {
                        showError(data.error || 'Failed to create account');
                    }
                } catch (error) {
                    console.error('Signup error:', error);
                    showError('Network error. Please check your connection and try again.');
                } finally {
                    setLoadingState(false);
                }
            });

            // Utility functions
            function setLoadingState(isLoading) {
                signupButton.disabled = isLoading;
                buttonText.style.display = isLoading ? 'none' : 'block';
                loading.style.display = isLoading ? 'block' : 'none';
            }

            function showError(message) {
                errorMessage.textContent = message;
                errorMessage.style.display = 'block';
                successMessage.style.display = 'none';
            }

            function showSuccess(message) {
                successMessage.textContent = message;
                successMessage.style.display = 'block';
                errorMessage.style.display = 'none';
            }

            function hideMessages() {
                errorMessage.style.display = 'none';
                successMessage.style.display = 'none';
            }

            // Focus on email input
            emailInput.focus();
        });
    </script>
</body>
</html>
